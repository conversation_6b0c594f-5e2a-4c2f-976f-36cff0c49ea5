{"Version": 3, "Meta": {"Duration": 3.433, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 106, "TotalPointCount": 255, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -30, 1, 0.667, -30, 1.333, -15, 2, -15, 0, 3.433, -15]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.667, 0, 1.333, 0, 2, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.1, 0, 0.2, -11.037, 0.3, -14, 1, 0.711, -26.182, 1.122, -30, 1.533, -30, 1, 1.689, -30, 1.844, -1.721, 2, 20, 1, 2.078, 30.861, 2.156, 30, 2.233, 30, 1, 2.389, 30, 2.544, 21, 2.7, 21, 0, 3.433, 21]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 1.167, 1, 1, 1.233, 1.008, 1.3, -0.008, 1.367, 0, 0, 1.667, 0, 1, 1.744, -0.008, 1.822, 1.008, 1.9, 1, 0, 2.633, 1, 1, 2.678, 1.008, 2.722, -0.008, 2.767, 0, 0, 2.833, 0, 1, 2.889, -0.008, 2.944, 1.008, 3, 1, 0, 3.367, 1, 0, 3.433, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 1.167, 1, 1, 1.233, 1.008, 1.3, -0.008, 1.367, 0, 0, 1.667, 0, 1, 1.744, -0.008, 1.822, 1.008, 1.9, 1, 0, 2.633, 1, 1, 2.678, 1.008, 2.722, -0.008, 2.767, 0, 0, 2.833, 0, 1, 2.889, -0.008, 2.944, 1.008, 3, 1, 0, 3.367, 1, 0, 3.433, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.7, 1, 0.378, 0.7, 0.756, 0, 1.133, 0, 1, 1.422, 0, 1.711, 1, 2, 1, 0, 3.433, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.378, 0, 0.756, 0, 1.133, 0, 1, 1.422, 0, 1.711, 0, 2, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.667, 0, 1.333, 0.16, 2, 0.16, 0, 3.433, 0.16]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.089, 0, 0.178, -0.38, 0.267, -0.38, 0, 3.433, -0.38]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.089, 0, 0.178, -0.39, 0.267, -0.39, 0, 3.433, -0.39]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0.013, 1, 0.233, 0.061, 0.267, -0.072, 0.3, 0.167, 1, 0.333, 0.4, 0.367, 0.855, 0.4, 0.855, 1, 0.489, 0.854, 0.578, 0.722, 0.667, 0.722, 1, 0.7, 0.706, 0.733, 0.935, 0.767, 1, 0, 1.033, 1, 1, 1.067, 0.971, 1.1, 0.809, 1.133, 0.745, 1, 1.167, 0.664, 1.2, 0.597, 1.233, 0.531, 1, 1.3, 0.404, 1.367, 0.306, 1.433, 0.231, 1, 1.578, 0.068, 1.722, 0.016, 1.867, 0.016, 1, 1.933, 0.012, 2, 0.514, 2.067, 0.51, 1, 2.111, 0.511, 2.156, 0.336, 2.2, 0.337, 1, 2.233, 0.155, 2.267, 0.939, 2.3, 1, 0, 2.333, 1, 2, 2.367, 0.843, 1, 2.378, 0.534, 2.389, 0.173, 2.4, 0.188, 1, 2.411, 0.175, 2.422, 0.496, 2.433, 0.773, 3, 2.467, 1, 2, 2.5, 0.918, 1, 2.511, 0.71, 2.522, 0.468, 2.533, 0.478, 1, 2.556, 0.481, 2.578, 0.636, 2.6, 0.659, 1, 2.622, 0.679, 2.644, 0.695, 2.667, 0.708, 1, 2.689, 0.723, 2.711, 0.734, 2.733, 0.749, 1, 2.767, 0.825, 2.8, 0.658, 2.833, 1, 0, 2.933, 1, 1, 2.956, 0.992, 2.978, 0.989, 3, 0.965, 1, 3.022, 0.931, 3.044, 0.704, 3.067, 0.533, 1, 3.111, 0.207, 3.156, -0.003, 3.2, 0, 0, 3.367, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -10, 1, 0.411, -10, 0.822, -10, 1.233, -10, 1, 1.489, -10, 1.744, -5, 2, -5, 1, 2.322, -5, 2.644, -6, 2.967, -6, 0, 3.433, -6]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.078, 0, 0.156, 10, 0.233, 10, 1, 0.411, 10, 0.589, -5, 0.767, -5, 1, 1, -5, 1.233, 0, 1.467, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.267, 0, 0.533, -3, 0.8, -3, 1, 1.267, -3, 1.733, 4, 2.2, 4, 1, 2.444, 4, 2.689, 3, 2.933, 3, 0, 3.433, 3]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.433, 0, 0.867, 0, 1.3, 0, 1, 1.544, 0, 1.789, 10, 2.033, 10, 1, 2.378, 10, 2.722, 0, 3.067, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.433, 0, 0.867, 0, 1.3, 0, 1, 1.544, 0, 1.789, 10, 2.033, 10, 1, 2.378, 10, 2.722, 0, 3.067, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 3.433, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 3.433, 0]}]}