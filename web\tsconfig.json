{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "strictNullChecks": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@live2dFramework/*": ["./app/lib/live2d/Framework/src/*"], "@live2dCore/*": ["./app/lib/live2d/Core/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}