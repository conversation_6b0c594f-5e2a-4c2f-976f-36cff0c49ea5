{"Version": 3, "Meta": {"Duration": 1.9, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 107, "TotalPointCount": 283, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.778, 0, 0.856, 0, 0.933, 0, 1, 1.111, 0, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 11, 0.333, 11, 1, 0.456, 11, 0.578, -30, 0.7, -30, 1, 0.778, -30, 0.856, -30, 0.933, -30, 1, 1.011, -30, 1.089, -30, 1.167, -30, 1, 1.267, -30, 1.367, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 1.111, 0, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.456, 1, 0.578, 0.91, 0.7, 0.91, 1, 0.778, 0.91, 0.856, 0.91, 0.933, 0.91, 1, 1.111, 0.91, 1.289, 1, 1.467, 1, 0, 1.9, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.456, 1, 0.578, 0.91, 0.7, 0.91, 1, 0.778, 0.91, 0.856, 0.91, 0.933, 0.91, 1, 1.111, 0.91, 1.289, 1, 1.467, 1, 0, 1.9, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.02, 0.333, -0.02, 1, 0.456, -0.02, 0.578, 0, 0.7, 0, 1, 0.778, 0, 0.856, 0, 0.933, 0, 1, 1.111, 0, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.42, 0.333, -0.42, 1, 0.456, -0.42, 0.578, 1, 0.7, 1, 1, 0.778, 1, 0.856, 1, 0.933, 1, 1, 1.111, 1, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -0.33, 0.7, -0.33, 1, 0.778, -0.33, 0.856, -0.33, 0.933, -0.33, 1, 1.111, -0.33, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -0.31, 0.7, -0.31, 1, 0.778, -0.31, 0.856, -0.31, 0.933, -0.31, 1, 1.111, -0.31, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -0.71, 0.7, -0.71, 1, 0.778, -0.71, 0.856, -0.71, 0.933, -0.71, 0, 1.9, -0.71]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -0.73, 0.7, -0.73, 1, 0.778, -0.73, 0.856, -0.73, 0.933, -0.73, 0, 1.9, -0.73]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -1, 0.7, -1, 1, 0.778, -1, 0.856, -1, 0.933, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -1, 0.7, -1, 1, 0.778, -1, 0.856, -1, 0.933, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -1, 0.7, -1, 1, 0.778, -1, 0.856, -1, 0.933, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -1, 0.7, -1, 1, 0.778, -1, 0.856, -1, 0.933, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.311, -1, 0.622, -1, 0.933, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.489, 0, 0.978, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamRage", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 1.111, 0, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 1.111, 0, 1.289, 0, 1.467, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 4, 0.333, 4, 1, 0.367, 4, 0.4, 4, 0.433, 4, 1, 0.522, 4, 0.611, -10, 0.7, -10, 1, 0.778, -10, 0.856, -10, 0.933, -10, 1, 1.011, -10, 1.089, -9.957, 1.167, -8, 1, 1.222, -6.602, 1.278, 3, 1.333, 3, 1, 1.422, 3, 1.511, 0, 1.6, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 20, 1, 0.311, 20, 0.622, 20, 0.933, 20, 1, 1.111, 20, 1.289, 20, 1.467, 20, 0, 1.9, 20]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 20, 1, 0.311, 20, 0.622, 20, 0.933, 20, 1, 1.111, 20, 1.289, 20, 1.467, 20, 0, 1.9, 20]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_L", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_R", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_L", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_R", "Segments": [0, 0, 0, 1.9, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_FACE_001_c", "Segments": [0, 0, 0, 1.9, 0]}]}