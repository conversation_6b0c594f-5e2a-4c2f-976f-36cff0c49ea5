import React, { useState, useEffect } from 'react';

export const useSimpleDisclosure = ({ isOpen: initialIsOpen = false, onClose } = {}) => {
  const [isOpen, setIsOpen] = useState(initialIsOpen);
  
  useEffect(() => {
    setIsOpen(initialIsOpen);
  }, [initialIsOpen]);
  
  const onOpen = () => setIsOpen(true);
  
  const onOpenChange = (open) => {
    setIsOpen(open);
    if (!open && onClose) {
      onClose();
    }
  };
  
  return { isOpen, onOpen, onOpenChange };
};

export const SimpleModal = ({ 
  children, 
  isOpen, 
  onOpenChange,
  size = 'md',
  placement = 'center',
  scrollBehavior = 'inside',
  className = '', 
  ...props 
}) => {
  if (!isOpen) return null;
  
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onOpenChange(false);
    }
  };
  
  const backdropStyles = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: placement === 'top' ? 'flex-start' : placement === 'bottom' ? 'flex-end' : 'center',
    justifyContent: 'center',
    zIndex: 1000,
    padding: '1rem',
    overflow: scrollBehavior === 'outside' ? 'auto' : 'hidden',
  };
  
  const modalStyles = {
    backgroundColor: 'white',
    borderRadius: '0.5rem',
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    width: size === 'xs' ? '20rem' : 
           size === 'sm' ? '24rem' : 
           size === 'md' ? '28rem' : 
           size === 'lg' ? '32rem' : 
           size === 'xl' ? '36rem' : 
           size === '2xl' ? '42rem' : 
           size === '3xl' ? '48rem' : 
           size === '4xl' ? '56rem' : 
           size === '5xl' ? '64rem' : 
           size === 'full' ? '100%' : '28rem',
    maxWidth: '100%',
    maxHeight: scrollBehavior === 'inside' ? '90vh' : 'none',
    overflow: scrollBehavior === 'inside' ? 'auto' : 'visible',
    display: 'flex',
    flexDirection: 'column',
  };
  
  return (
    <div 
      style={backdropStyles} 
      onClick={handleBackdropClick}
      className={className}
      {...props}
    >
      <div style={modalStyles} onClick={(e) => e.stopPropagation()}>
        {typeof children === 'function' 
          ? children(() => onOpenChange(false)) 
          : children
        }
      </div>
    </div>
  );
};

export const SimpleModalContent = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
};

export const SimpleModalHeader = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const styles = {
    padding: '1rem',
    borderBottom: '1px solid #e2e8f0',
    fontWeight: 'bold',
    fontSize: '1.25rem',
  };
  
  return (
    <div style={styles} className={className} {...props}>
      {children}
    </div>
  );
};

export const SimpleModalBody = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const styles = {
    padding: '1rem',
    flex: '1 1 auto',
  };
  
  return (
    <div style={styles} className={className} {...props}>
      {children}
    </div>
  );
};

export const SimpleModalFooter = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const styles = {
    padding: '1rem',
    borderTop: '1px solid #e2e8f0',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '0.5rem',
  };
  
  return (
    <div style={styles} className={className} {...props}>
      {children}
    </div>
  );
};

export const SimpleButton = ({ 
  children, 
  color = 'default',
  variant = 'solid',
  onPress,
  className = '', 
  ...props 
}) => {
  const baseStyles = {
    padding: '0.5rem 1rem',
    borderRadius: '0.375rem',
    fontWeight: 'medium',
    cursor: 'pointer',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s',
  };
  
  const colorStyles = {
    default: {
      solid: { backgroundColor: '#e2e8f0', color: '#1a202c' },
      light: { backgroundColor: 'transparent', color: '#1a202c' },
    },
    primary: {
      solid: { backgroundColor: '#3182ce', color: 'white' },
      light: { backgroundColor: 'transparent', color: '#3182ce' },
    },
    success: {
      solid: { backgroundColor: '#38a169', color: 'white' },
      light: { backgroundColor: 'transparent', color: '#38a169' },
    },
    danger: {
      solid: { backgroundColor: '#e53e3e', color: 'white' },
      light: { backgroundColor: 'transparent', color: '#e53e3e' },
    },
    warning: {
      solid: { backgroundColor: '#dd6b20', color: 'white' },
      light: { backgroundColor: 'transparent', color: '#dd6b20' },
    },
  };
  
  const styles = {
    ...baseStyles,
    ...colorStyles[color][variant],
  };
  
  return (
    <button 
      style={styles} 
      className={className} 
      onClick={onPress}
      {...props}
    >
      {children}
    </button>
  );
};

export default SimpleModal;
