version: '3'
services:
  # awesome digital human web
  adh_web:
    build:
      context: .
      dockerfile: docker/adhWeb.Dockerfile
    image: "adh-web:v2.0.0"
    restart: always
    # network_mode: host
    ports:
      - "3000:3000"
    volumes:
      - ./web/.env:/workspace/.env
  # awesome digital human server
  adh_server:
    build:
      context: .
      dockerfile: docker/adhServer.Dockerfile
    image: "adh-server:v2.0.0"
    restart: always
    # network_mode: host
    ports:
      - "8000:8000"
    volumes:
      - ./configs:/workspace/configs
