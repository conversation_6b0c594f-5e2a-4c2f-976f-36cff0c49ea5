{"Version": 3, "Meta": {"Duration": 5, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 72, "TotalSegmentCount": 387, "TotalPointCount": 1109, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 0.17, 1, 0.33, 1, 0.5, 1, 1, 1.07, 1, 1.63, 1, 2.2, 1, 1, 2.3, 1, 2.4, 1, 2.5, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamMoveX", "Segments": [0, -38.4, 1, 0.189, -38.4, 0.378, -39.957, 0.567, -34.2, 1, 0.878, -24.718, 1.189, 59.6, 1.5, 59.6, 1, 1.711, 59.6, 1.922, 33.4, 2.133, 33.4, 0, 5, 33.4]}, {"Target": "Parameter", "Id": "ParamMoveY", "Segments": [0, 69.4, 1, 0.189, 69.4, 0.378, 65.391, 0.567, 58, 1, 0.733, 51.479, 0.9, 48.511, 1.067, 48.511, 1, 1.211, 48.511, 1.356, 52.813, 1.5, 56.4, 1, 1.611, 59.159, 1.722, 59.397, 1.833, 59.397, 1, 1.933, 59.397, 2.033, 53.8, 2.133, 53.8, 1, 2.256, 53.8, 2.378, 54.8, 2.5, 54.8, 1, 2.622, 54.8, 2.744, 54.2, 2.867, 54.2, 0, 5, 54.2]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 6.86, 1, 0.4, 6.86, 0.8, 6.838, 1.2, 6, 1, 1.4, 5.581, 1.6, -5, 1.8, -5, 0, 5, -5]}, {"Target": "Parameter", "Id": "ParamScaling", "Segments": [0, 10, 1, 0.189, 10, 0.378, 7.795, 0.567, 0, 1, 0.678, -4.585, 0.789, -10, 0.9, -10, 1, 1.011, -10, 1.122, -10, 1.233, -10, 1, 1.322, -10, 1.411, -5.457, 1.5, 0, 1, 1.622, 7.503, 1.744, 10, 1.867, 10, 0, 5, 10]}, {"Target": "Parameter", "Id": "ParamEffect1", "Segments": [0, 0, 1, 0.067, 0, 0.133, 4, 0.2, 4, 1, 0.278, 4, 0.356, -1, 0.433, -1, 1, 0.5, -1, 0.567, 3, 0.633, 3, 1, 0.711, 3, 0.789, -2, 0.867, -2, 1, 0.933, -2, 1, 3, 1.067, 3, 1, 1.156, 3, 1.244, 0, 1.333, 0, 1, 1.4, 0, 1.467, 4, 1.533, 4, 1, 1.611, 4, 1.689, -1, 1.767, -1, 1, 1.833, -1, 1.9, 3, 1.967, 3, 1, 2.044, 3, 2.122, -2, 2.2, -2, 1, 2.267, -2, 2.333, 3, 2.4, 3, 1, 2.489, 3, 2.578, 0, 2.667, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 29.7, 1, 0.6, 29.7, 1.2, -30, 1.8, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamFlapping8", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0, 0.533, 0, 1, 0.644, 0, 0.756, -16, 0.867, -16, 1, 0.978, -16, 1.089, 30, 1.2, 30, 1, 1.311, 30, 1.422, 20, 1.533, 0, 1, 1.644, -20, 1.756, -30, 1.867, -30, 1, 1.978, -30, 2.089, -20, 2.2, 0, 1, 2.311, 20, 2.422, 30, 2.533, 30, 1, 2.644, 30, 2.756, 0, 2.867, 0, 1, 2.922, 0, 2.978, 24.66, 3.033, 24.66, 1, 3.089, 24.66, 3.144, 15.945, 3.2, 0, 1, 3.256, -15.945, 3.311, -23.22, 3.367, -23.22, 1, 3.411, -23.22, 3.456, -14.089, 3.5, 0, 1, 3.556, 17.611, 3.611, 24.66, 3.667, 24.66, 1, 3.722, 24.66, 3.778, 15.945, 3.833, 0, 1, 3.889, -15.945, 3.944, -23.22, 4, -23.22, 1, 4.044, -23.22, 4.089, -14.089, 4.133, 0, 1, 4.189, 17.611, 4.244, 24.66, 4.3, 24.66, 1, 4.356, 24.66, 4.411, 15.945, 4.467, 0, 1, 4.522, -15.945, 4.578, -23.22, 4.633, -23.22, 1, 4.678, -23.22, 4.722, -14.089, 4.767, 0, 1, 4.822, 17.611, 4.878, 24.66, 4.933, 24.66, 1, 4.956, 24.66, 4.978, 23.266, 5, 20.572]}, {"Target": "Parameter", "Id": "ParamFlapping7", "Segments": [0, 30, 1, 0.111, 30, 0.222, -30, 0.333, -30, 1, 0.444, -30, 0.556, -16.42, 0.667, -11, 1, 0.778, -5.58, 0.889, -5.758, 1, 0, 1, 1.111, 5.758, 1.222, 30, 1.333, 30, 1, 1.444, 30, 1.556, 20, 1.667, 0, 1, 1.778, -20, 1.889, -30, 2, -30, 1, 2.111, -30, 2.222, -20, 2.333, 0, 1, 2.444, 20, 2.556, 30, 2.667, 30, 1, 2.733, 30, 2.8, 27.192, 2.867, 19.98, 1, 2.922, 13.97, 2.978, 9.021, 3.033, 0, 1, 3.089, -9.021, 3.144, -21.84, 3.2, -21.84, 1, 3.256, -21.84, 3.311, -15.416, 3.367, 0, 1, 3.411, 12.333, 3.456, 19.98, 3.5, 19.98, 1, 3.556, 19.98, 3.611, 13.911, 3.667, 0, 1, 3.722, -13.911, 3.778, -21.84, 3.833, -21.84, 1, 3.889, -21.84, 3.944, -15.416, 4, 0, 1, 4.044, 12.333, 4.089, 19.98, 4.133, 19.98, 1, 4.189, 19.98, 4.244, 13.911, 4.3, 0, 1, 4.356, -13.911, 4.411, -21.84, 4.467, -21.84, 1, 4.522, -21.84, 4.578, -15.416, 4.633, 0, 1, 4.678, 12.333, 4.722, 19.98, 4.767, 19.98, 1, 4.822, 19.98, 4.878, 13.911, 4.933, 0, 1, 4.956, -5.564, 4.978, -10.172, 5, -13.697]}, {"Target": "Parameter", "Id": "ParamFlapping4", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0, 0.533, 0, 1, 0.644, 0, 0.756, -30, 0.867, -30, 1, 0.967, -30, 1.067, -17.806, 1.167, 0, 1, 1.289, 21.763, 1.411, 30, 1.533, 30, 1, 1.644, 30, 1.756, 20, 1.867, 0, 1, 1.978, -20, 2.089, -30, 2.2, -30, 1, 2.311, -30, 2.422, 0, 2.533, 0, 1, 2.644, 0, 2.756, 0, 2.867, 0, 1, 2.922, 0, 2.978, 24.78, 3.033, 24.78, 1, 3.089, 24.78, 3.144, 16.172, 3.2, 0, 1, 3.256, -16.172, 3.311, -23.76, 3.367, -23.76, 1, 3.411, -23.76, 3.456, -14.26, 3.5, 0, 1, 3.556, 17.825, 3.611, 24.78, 3.667, 24.78, 1, 3.722, 24.78, 3.778, 16.172, 3.833, 0, 1, 3.889, -16.172, 3.944, -23.76, 4, -23.76, 1, 4.044, -23.76, 4.089, -14.26, 4.133, 0, 1, 4.189, 17.825, 4.244, 24.78, 4.3, 24.78, 1, 4.356, 24.78, 4.411, 16.172, 4.467, 0, 1, 4.522, -16.172, 4.578, -23.76, 4.633, -23.76, 1, 4.678, -23.76, 4.722, -14.26, 4.767, 0, 1, 4.822, 17.825, 4.878, 24.78, 4.933, 24.78, 1, 4.956, 24.78, 4.978, 23.403, 5, 20.715]}, {"Target": "Parameter", "Id": "ParamFlapping3", "Segments": [0, 0, 1, 0.111, 0, 0.222, 30, 0.333, 30, 1, 0.444, 30, 0.556, 20.99, 0.667, 0, 1, 0.767, -18.891, 0.867, -30, 0.967, -30, 1, 1.089, -30, 1.211, -20.901, 1.333, 0, 1, 1.444, 19.001, 1.556, 30, 1.667, 30, 1, 1.778, 30, 1.889, 20, 2, 0, 1, 2.111, -20, 2.222, -30, 2.333, -30, 1, 2.444, -30, 2.556, -20.746, 2.667, 0, 1, 2.733, 12.447, 2.8, 19.92, 2.867, 19.92, 1, 2.922, 19.92, 2.978, 11.878, 3.033, 0, 1, 3.089, -11.878, 3.144, -16.14, 3.2, -16.14, 1, 3.256, -16.14, 3.311, -12.715, 3.367, 0, 1, 3.411, 10.172, 3.456, 19.92, 3.5, 19.92, 1, 3.556, 19.92, 3.611, 11.878, 3.667, 0, 1, 3.722, -11.878, 3.778, -16.14, 3.833, -16.14, 1, 3.889, -16.14, 3.944, -12.715, 4, 0, 1, 4.044, 10.172, 4.089, 19.92, 4.133, 19.92, 1, 4.189, 19.92, 4.244, 11.878, 4.3, 0, 1, 4.356, -11.878, 4.411, -16.14, 4.467, -16.14, 1, 4.522, -16.14, 4.578, -12.715, 4.633, 0, 1, 4.678, 10.172, 4.722, 19.92, 4.767, 19.92, 1, 4.822, 19.92, 4.878, 11.878, 4.933, 0, 1, 4.956, -4.751, 4.978, -8.284, 5, -10.813]}, {"Target": "Parameter", "Id": "ParamFairyDO", "Segments": [0, 0, 1, 0.189, 0, 0.378, -1, 0.567, -1, 1, 0.878, -1, 1.189, -1, 1.5, -1, 1, 1.622, -1, 1.744, 1, 1.867, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight1", "Segments": [0, 0, 0, 5, 10]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight2", "Segments": [0, 0, 0, 5, 10]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight3", "Segments": [0, 0, 0, 5, 10]}, {"Target": "Parameter", "Id": "ParamWaterSurface1", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.367, 0, 2.533, 2, 2.7, 2, 1, 2.9, 2, 3.1, 0, 3.3, 0, 1, 3.533, 0, 3.767, 1, 4, 1, 1, 4.333, 1, 4.667, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurface2", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.367, 0, 2.533, 3, 2.7, 3, 1, 2.9, 3, 3.1, 0, 3.3, 0, 1, 3.533, 0, 3.767, 1, 4, 1, 1, 4.333, 1, 4.667, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLower1", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.367, 0, 2.533, 3, 2.7, 3, 1, 2.9, 3, 3.1, 0, 3.3, 0, 1, 3.533, 0, 3.767, 1, 4, 1, 1, 4.333, 1, 4.667, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLower2", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.367, 0, 2.533, 2, 2.7, 2, 1, 2.9, 2, 3.1, 0, 3.3, 0, 1, 3.533, 0, 3.767, 1, 4, 1, 1, 4.333, 1, 4.667, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceBack1", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.367, 0, 2.533, 2, 2.7, 2, 1, 2.9, 2, 3.1, 0, 3.3, 0, 1, 3.533, 0, 3.767, 1, 4, 1, 1, 4.333, 1, 4.667, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceBack2", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.367, 0, 2.533, 2, 2.7, 2, 1, 2.9, 2, 3.1, 0, 3.3, 0, 1, 3.533, 0, 3.767, 1, 4, 1, 1, 4.333, 1, 4.667, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.089, 0, 0.178, -6.209, 0.267, -8, 1, 0.367, -10.015, 0.467, -9.699, 0.567, -12, 1, 0.711, -15.324, 0.856, -24, 1, -24, 1, 1.167, -24, 1.333, -12.109, 1.5, 5, 1, 1.6, 15.265, 1.7, 18, 1.8, 18, 1, 1.922, 18, 2.044, 18, 2.167, 18, 0, 5, 18]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 6, 0.267, 6, 1, 0.367, 6, 0.467, -28, 0.567, -28, 1, 0.711, -28, 0.856, 16, 1, 16, 1, 1.167, 16, 1.333, 15.018, 1.5, 5, 1, 1.6, -1.011, 1.7, -30, 1.8, -30, 1, 1.922, -30, 2.044, -25, 2.167, -25, 0, 5, -25]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.189, 0, 0.378, -0.35, 0.567, -7, 1, 0.878, -17.953, 1.189, -30, 1.5, -30, 1, 1.6, -30, 1.7, -30, 1.8, -30, 1, 1.922, -30, 2.044, -27, 2.167, -27, 0, 5, -27]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.711, 0, 0.856, -0.5, 1, -0.5, 1, 1.167, -0.5, 1.333, 0, 1.5, 0, 1, 1.6, 0, 1.7, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.711, 0, 0.856, 0.2, 1, 0.2, 1, 1.167, 0.2, 1.333, 0, 1.5, 0, 1, 1.6, 0, 1.7, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.189, 1, 0.378, 1, 0.567, 1, 1, 0.978, 1, 1.389, 1, 1.8, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.189, 1, 0.378, 1, 0.567, 1, 1, 0.978, 1, 1.389, 1, 1.8, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.7, 1, 0.189, -0.7, 0.378, -0.648, 0.567, -0.5, 1, 0.711, -0.386, 0.856, -0.272, 1, -0.1, 1, 1.167, 0.099, 1.333, 0.3, 1.5, 0.3, 1, 1.6, 0.3, 1.7, 0.3, 1.8, 0.3, 0, 5, 0.3]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.2, 1, 0.189, 0.2, 0.378, 0.1, 0.567, 0.1, 1, 0.711, 0.1, 0.856, 0.5, 1, 0.5, 1, 1.167, 0.5, 1.333, -1, 1.5, -1, 1, 1.6, -1, 1.7, -0.5, 1.8, -0.5, 0, 5, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.189, 0, 0.378, -0.516, 0.567, -2, 1, 0.711, -3.135, 0.856, -5.217, 1, -6, 1, 1.167, -6.903, 1.333, -7, 1.5, -7, 1, 1.6, -7, 1.7, -3, 1.8, -3, 1, 1.922, -3, 2.044, -4, 2.167, -4, 0, 5, -4]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0.083, 0.567, -1, 1, 0.711, -1.828, 0.856, -10, 1, -10, 1, 1.167, -10, 1.333, -7, 1.5, -7, 1, 1.6, -7, 1.7, -7, 1.8, -7, 0, 5, -7]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmL1", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.711, 0, 0.856, 18, 1, 18, 1, 1.167, 18, 1.333, 16.927, 1.5, 11, 1, 1.6, 7.444, 1.7, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmL2", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, 22, 1.1, 22, 1, 1.356, 22, 1.611, 0, 1.867, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmL3", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, 0, 1.1, 0, 1, 1.233, 0, 1.367, 30, 1.5, 30, 1, 1.689, 30, 1.878, -10, 2.067, -10, 0, 5, -10]}, {"Target": "Parameter", "Id": "ParamFingerL1X", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, 21, 1.1, 21, 1, 1.389, 21, 1.678, -30, 1.967, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamFingerL1Z", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, 30, 1.1, 30, 1, 1.256, 30, 1.411, 30, 1.567, 30, 1, 1.722, 30, 1.878, 0, 2.033, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamFingerL2", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, -30, 1.1, -30, 1, 1.256, -30, 1.411, -30, 1.567, -30, 1, 1.756, -30, 1.944, 0, 2.133, 0, 1, 2.7, 0, 3.267, -6, 3.833, -6, 0, 5, -6]}, {"Target": "Parameter", "Id": "ParamFingerL3", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, -23, 1.1, -23, 1, 1.256, -23, 1.411, -23, 1.567, -23, 1, 1.789, -23, 2.011, 0, 2.233, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamFingerL4", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, -30, 1.1, -30, 1, 1.256, -30, 1.411, -30, 1.567, -30, 1, 1.833, -30, 2.1, 0, 2.367, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmR1", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.711, 0, 0.856, 26, 1, 26, 1, 1.167, 26, 1.333, -26, 1.5, -26, 1, 1.6, -26, 1.7, -26, 1.8, -26, 0, 5, -26]}, {"Target": "Parameter", "Id": "ParamArmR2", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.711, 0, 0.856, -1.493, 1, -9, 1, 1.167, -17.662, 1.333, -26, 1.5, -26, 1, 1.6, -26, 1.7, -26, 1.8, -26, 0, 5, -26]}, {"Target": "Parameter", "Id": "ParamArmR3", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.656, 0, 0.744, -13, 0.833, -13, 1, 0.933, -13, 1.033, 4, 1.133, 4, 1, 1.211, 4, 1.289, -9.697, 1.367, -13, 1, 1.511, -19.135, 1.656, -20, 1.8, -20, 0, 5, -20]}, {"Target": "Parameter", "Id": "ParamFingerR1", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.733, 0, 0.9, 0, 1.067, 0, 1, 1.111, 0, 1.156, 0, 1.2, 0, 1, 1.4, 0, 1.6, -30, 1.8, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamFingerR2", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.733, 0, 0.9, 0, 1.067, 0, 1, 1.111, 0, 1.156, -15.684, 1.2, -20, 1, 1.289, -28.632, 1.378, -30, 1.467, -30, 1, 1.578, -30, 1.689, -30, 1.8, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamFingerR3", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.733, 0, 0.9, 0, 1.067, 0, 1, 1.144, 0, 1.222, -30, 1.3, -30, 1, 1.356, -30, 1.411, -30, 1.467, -30, 1, 1.578, -30, 1.689, -30, 1.8, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamFingerR4", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.733, 0, 0.9, 0, 1.067, 0, 1, 1.2, 0, 1.333, -30, 1.467, -30, 1, 1.578, -30, 1.689, -30, 1.8, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamLegL1X", "Segments": [0, 0, 1, 0.189, 0, 0.378, -2.711, 0.567, -8, 1, 0.878, -16.711, 1.189, -21, 1.5, -21, 1, 1.6, -21, 1.7, -21, 1.8, -21, 0, 5, -21]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 1, 0.189, 0, 0.378, 1, 0.567, 1, 1, 0.978, 1, 1.389, 1, 1.8, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamLegL2X", "Segments": [0, 0, 1, 0.189, 0, 0.378, -10, 0.567, -10, 1, 0.978, -10, 1.389, -10, 1.8, -10, 0, 5, -10]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 1, 0.189, 0, 0.378, -9, 0.567, -9, 1, 0.978, -9, 1.389, -9, 1.8, -9, 0, 5, -9]}, {"Target": "Parameter", "Id": "ParamLegL3X", "Segments": [0, 0, 1, 0.189, 0, 0.378, 30, 0.567, 30, 1, 0.978, 30, 1.389, 30, 1.8, 30, 0, 5, 30]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 1, 0.189, 0, 0.378, -30, 0.567, -30, 1, 0.978, -30, 1.389, -30, 1.8, -30, 0, 5, -30]}, {"Target": "Parameter", "Id": "ParamLegLDO", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 1, 0.189, 0, 0.378, 2.362, 0.567, 8, 1, 0.711, 12.311, 0.856, 15, 1, 15, 1, 1.167, 15, 1.333, 14.472, 1.5, 9, 1, 1.6, 5.717, 1.7, -11, 1.8, -11, 0, 5, -11]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 1, 0.189, 0, 0.378, -23, 0.567, -23, 1, 0.878, -23, 1.189, -8, 1.5, -8, 1, 1.6, -8, 1.7, -13, 1.8, -13, 0, 5, -13]}, {"Target": "Parameter", "Id": "ParamLegR2X", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.878, 0, 1.189, -30, 1.5, -30, 1, 1.6, -30, 1.7, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLegR2", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.711, 0, 0.856, 7, 1, 7, 1, 1.167, 7, 1.333, 7, 1.5, 7, 1, 1.6, 7, 1.7, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLegR3X", "Segments": [0, 1, 1, 0.189, 1, 0.378, 1, 0.567, 1, 1, 0.978, 1, 1.389, 1, 1.8, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.978, 0, 1.389, 0, 1.8, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamAllX", "Segments": [0, 0, 1, 0.189, 0, 0.378, -0.28, 0.567, 3, 1, 0.878, 8.403, 1.189, 17, 1.5, 17, 1, 1.6, 17, 1.7, 13.837, 1.8, 13, 1, 1.922, 11.978, 2.044, 12, 2.167, 12, 0, 5, 12]}, {"Target": "Parameter", "Id": "ParamAllY", "Segments": [0, 0, 1, 0.189, 0, 0.378, -2, 0.567, -2, 1, 0.878, -2, 1.189, -1, 1.5, -1, 1, 1.6, -1, 1.7, -1, 1.8, -1, 0, 5, -1]}, {"Target": "Parameter", "Id": "ParamAllRotate", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.878, 0, 1.189, 6, 1.5, 6, 1, 1.6, 6, 1.7, 3.803, 1.8, 3, 1, 1.922, 2.018, 2.044, 2, 2.167, 2, 0, 5, 2]}]}