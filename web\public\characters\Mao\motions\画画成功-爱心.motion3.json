{"Version": 3, "Meta": {"Duration": 7.8, "Fps": 30.0, "FadeInTime": 0.25, "FadeOutTime": 0.25, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 116, "TotalSegmentCount": 393, "TotalPointCount": 1053, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.3, 0.333, -1.7, 1, 0.456, -3.901, 0.578, -22.931, 0.7, -22.931, 1, 0.911, -22.931, 1.122, -21.931, 1.333, -21.931, 1, 1.533, -21.931, 1.733, -21.958, 1.933, -22.931, 1, 2.078, -23.634, 2.222, -25.931, 2.367, -25.931, 1, 2.589, -25.931, 2.811, -22.931, 3.033, -22.931, 1, 3.156, -22.931, 3.278, -24.931, 3.4, -24.931, 1, 3.544, -24.931, 3.689, -21.172, 3.833, -8, 1, 3.9, -1.92, 3.967, 7, 4.033, 7, 1, 4.133, 7, 4.233, -3, 4.333, -3, 1, 4.444, -3, 4.556, 0, 4.667, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, -2.583, 0.3, -2.583, 1, 0.433, -2.583, 0.567, 28, 0.7, 28, 1, 0.822, 28, 0.944, 25, 1.067, 25, 1, 1.4, 25, 1.733, 25, 2.067, 25, 1, 2.167, 25, 2.267, -8, 2.367, -8, 1, 2.567, -8, 2.767, 29, 2.967, 29, 1, 3.089, 29, 3.211, 11, 3.333, 11, 1, 3.478, 11, 3.622, 25, 3.767, 25, 1, 3.867, 25, 3.967, -12, 4.067, -12, 1, 4.156, -12, 4.244, -8.815, 4.333, -4, 1, 4.6, 10.444, 4.867, 17, 5.133, 17, 1, 5.489, 17, 5.844, 12, 6.2, 12, 0, 7.8, 12]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.233, 0, 0.467, -0.018, 0.7, 3, 1, 0.9, 5.587, 1.1, 14, 1.3, 14, 1, 1.478, 14, 1.656, 6.464, 1.833, 0, 1, 2, -6.06, 2.167, -7, 2.333, -7, 1, 2.422, -7, 2.511, 6.309, 2.6, 7, 1, 2.733, 8.037, 2.867, 8, 3, 8, 1, 3.144, 8, 3.289, -10, 3.433, -10, 1, 3.533, -10, 3.633, 2, 3.733, 2, 1, 3.933, 2, 4.133, 0, 4.333, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamFaceInkOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.3, 1, 0.333, 0, 0.367, 0, 1, 0.389, 0, 0.411, 0, 0.433, 0, 1, 0.467, 0, 0.5, 1, 0.533, 1, 1, 1.089, 1, 1.644, 1, 2.2, 1, 1, 2.244, 1, 2.289, 0, 2.333, 0, 1, 2.4, 0, 2.467, 0, 2.533, 0, 1, 2.578, 0, 2.622, 1, 2.667, 1, 1, 3.044, 1, 3.422, 1, 3.8, 1, 1, 3.833, 1, 3.867, 0, 3.9, 0, 1, 3.989, 0, 4.078, 0, 4.167, 0, 1, 4.2, 0, 4.233, 1.2, 4.267, 1.2, 0, 7.8, 1.2]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLForm", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.3, 1, 0.333, 0, 0.367, 0, 1, 0.389, 0, 0.411, 0, 0.433, 0, 1, 0.467, 0, 0.5, 1, 0.533, 1, 1, 1.089, 1, 1.644, 1, 2.2, 1, 1, 2.244, 1, 2.289, 0, 2.333, 0, 1, 2.4, 0, 2.467, 0, 2.533, 0, 1, 2.578, 0, 2.622, 1, 2.667, 1, 1, 3.044, 1, 3.422, 1, 3.8, 1, 1, 3.833, 1, 3.867, 0, 3.9, 0, 1, 3.989, 0, 4.078, 0, 4.167, 0, 1, 4.2, 0, 4.233, 1.2, 4.267, 1.2, 0, 7.8, 1.2]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeRForm", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -0.2, 0.7, -0.2, 1, 1.811, -0.2, 2.922, -0.2, 4.033, -0.2, 1, 4.078, -0.2, 4.122, 0, 4.167, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0.2, 0.7, 0.2, 1, 1.811, 0.2, 2.922, 0.2, 4.033, 0.2, 1, 4.078, 0.2, 4.122, 0, 4.167, 0, 1, 4.2, 0, 4.233, 0.5, 4.267, 0.5, 0, 7.8, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeEffect", "Segments": [0, 0, 1, 1.344, 0, 2.689, 0, 4.033, 0, 1, 4.111, 0, 4.189, 1, 4.267, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 1.378, 0, 2.756, 0, 4.133, 0, 1, 4.211, 0, 4.289, 0.6, 4.367, 0.6, 0, 7.8, 0.6]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 1.378, 0, 2.756, 0, 4.133, 0, 1, 4.211, 0, 4.289, 0.6, 4.367, 0.6, 0, 7.8, 0.6]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthA", "Segments": [0, 0, 1, 1.267, 0, 2.533, 0, 3.8, 0, 1, 3.978, 0, 4.156, 1, 4.333, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamMouthI", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthU", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthE", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthO", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthUp", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamMouthDown", "Segments": [0, 0, 1, 1.233, 0, 2.467, 0, 3.7, 0, 1, 3.789, 0, 3.878, 0.4, 3.967, 0.4, 1, 4.089, 0.4, 4.211, 0, 4.333, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthAngry", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthAngryLine", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.433, 0, 0.867, -5, 1.3, -5, 1, 1.633, -5, 1.967, -3, 2.3, -3, 1, 2.589, -3, 2.878, -3, 3.167, -3, 1, 3.278, -3, 3.389, -4, 3.5, -4, 1, 3.722, -4, 3.944, 1, 4.167, 1, 1, 4.289, 1, 4.411, 0, 4.533, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, -1, 0.3, -1, 1, 0.4, -1, 0.5, 4.328, 0.6, 7, 1, 0.678, 9.079, 0.756, 9, 0.833, 9, 1, 0.978, 9, 1.122, 5, 1.267, 5, 1, 1.344, 5, 1.422, 6.297, 1.5, 7, 1, 1.6, 7.904, 1.7, 8, 1.8, 8, 1, 1.944, 8, 2.089, 3.885, 2.233, 0, 1, 2.278, -1.195, 2.322, -1, 2.367, -1, 1, 2.533, -1, 2.7, 6.454, 2.867, 9, 1, 2.956, 10, 3.044, 10, 3.133, 10, 1, 3.211, 10, 3.289, 5, 3.367, 5, 1, 3.467, 5, 3.567, 9, 3.667, 9, 1, 3.833, 9, 4, -4, 4.167, -4, 1, 4.311, -4, 4.456, 1.691, 4.6, 4, 1, 4.778, 6.842, 4.956, 7, 5.133, 7, 1, 5.456, 7, 5.778, 5, 6.1, 5, 0, 7.8, 5]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.378, 0, 0.756, 7, 1.133, 7, 1, 1.5, 7, 1.867, -1, 2.233, -1, 1, 2.356, -1, 2.478, 4.292, 2.6, 4.999, 1, 2.778, 6.026, 2.956, 5.999, 3.133, 5.999, 1, 3.222, 5.999, 3.311, 0, 3.4, 0, 1, 3.478, 0, 3.556, 0.016, 3.633, 1, 1, 3.722, 2.125, 3.811, 4, 3.9, 4, 1, 4.189, 4, 4.478, 0, 4.767, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamLeftShoulderUp", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.633, 0, 0.833, -1.626, 1.033, -1.626, 1, 1.311, -1.626, 1.589, -1.626, 1.867, -1.626, 1, 2.089, -1.626, 2.311, -3, 2.533, -3, 1, 2.667, -3, 2.8, 0, 2.933, 0, 1, 3.156, 0, 3.378, -0.149, 3.6, -1.626, 1, 3.8, -2.956, 4, -5, 4.2, -5, 1, 4.511, -5, 4.822, 0.891, 5.133, 0.891, 1, 5.344, 0.891, 5.556, 0, 5.767, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamRightShoulderUp", "Segments": [0, 0, 1, 0.156, 0, 0.311, -10, 0.467, -10, 1, 0.667, -10, 0.867, -8.333, 1.067, -8.333, 1, 1.222, -8.333, 1.378, -8.333, 1.533, -8.333, 1, 1.7, -8.333, 1.867, -10, 2.033, -10, 1, 2.278, -10, 2.522, 2, 2.767, 2, 1, 2.867, 2, 2.967, 2, 3.067, 2, 1, 3.222, 2, 3.378, 0, 3.533, 0, 1, 3.6, 0, 3.667, 0, 3.733, 0, 1, 3.9, 0, 4.067, -5.667, 4.233, -5.667, 1, 4.533, -5.667, 4.833, 0.677, 5.133, 0.677, 1, 5.344, 0.677, 5.556, 0, 5.767, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmLA01", "Segments": [0, 0, 1, 0.211, 0, 0.422, -1, 0.633, -1, 1, 0.856, -1, 1.078, 3, 1.3, 3, 1, 1.633, 3, 1.967, -3, 2.3, -3, 1, 2.556, -3, 2.811, 8, 3.067, 8, 1, 3.1, 8, 3.133, 8.298, 3.167, 7.32, 1, 3.233, 5.364, 3.3, -4, 3.367, -4, 1, 3.511, -4, 3.656, 0, 3.8, 0, 1, 3.911, 0, 4.022, -5.91, 4.133, -5.91, 1, 4.278, -5.91, 4.422, -1.199, 4.567, 1, 1, 4.7, 3.03, 4.833, 3, 4.967, 3, 1, 5.222, 3, 5.478, 0.803, 5.733, 0.803, 1, 5.989, 0.803, 6.244, 1.28, 6.5, 1.28, 0, 7.8, 1.28]}, {"Target": "Parameter", "Id": "ParamArmLA02", "Segments": [0, 0, 1, 0.289, 0, 0.578, -2, 0.867, -2, 1, 1.089, -2, 1.311, -1, 1.533, -1, 1, 1.811, -1, 2.089, -5, 2.367, -5, 1, 2.656, -5, 2.944, -2, 3.233, -2, 1, 3.333, -2, 3.433, -3, 3.533, -3, 1, 3.656, -3, 3.778, -2, 3.9, -2, 1, 4.011, -2, 4.122, -3, 4.233, -3, 1, 4.5, -3, 4.767, 2, 5.033, 2, 1, 5.311, 2, 5.589, -0.5, 5.867, -0.5, 1, 6.1, -0.5, 6.333, 0, 6.567, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmLA03", "Segments": [0, 0, 1, 0.3, 0, 0.6, -1, 0.9, -1, 1, 1.122, -1, 1.344, 0, 1.567, 0, 1, 1.844, 0, 2.122, -3, 2.4, -3, 1, 2.889, -3, 3.378, -2.71, 3.867, 0, 1, 4.122, 1.416, 4.378, 7, 4.633, 7, 1, 4.878, 7, 5.122, 5.5, 5.367, 5.5, 1, 5.733, 5.5, 6.1, 6, 6.467, 6, 0, 7.8, 6]}, {"Target": "Parameter", "Id": "ParamHandLA", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmRA01", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmRA02", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmRA03", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamWandRotate", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHandRA", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamInkDrop", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamInkDropRotate", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamInkDropOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmLB01", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmLB02", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmLB03", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHandLB", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHatForm", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmRB01", "Segments": [0, 0, 1, 0.178, 0, 0.356, -30, 0.533, -30, 1, 0.622, -30, 0.711, -13.204, 0.8, -3, 1, 0.889, 7.204, 0.978, 8, 1.067, 8, 1, 1.144, 8, 1.222, 3, 1.3, 3, 1, 1.378, 3, 1.456, 8, 1.533, 8, 1, 1.656, 8, 1.778, -4.681, 1.9, -11, 1, 2.056, -19.043, 2.211, -20, 2.367, -20, 1, 2.478, -20, 2.589, -8.612, 2.7, 0, 1, 2.822, 9.474, 2.944, 11, 3.067, 11, 1, 3.156, 11, 3.244, -12, 3.333, -12, 1, 3.389, -12, 3.444, -10, 3.5, -10, 1, 3.622, -10, 3.744, -10, 3.867, -10, 1, 3.989, -10, 4.111, -16, 4.233, -16, 1, 4.478, -16, 4.722, -10, 4.967, -10, 0, 7.8, -10]}, {"Target": "Parameter", "Id": "ParamArmRB02", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.16, 0.2, 1.053, 1, 0.289, 2.669, 0.378, 9.24, 0.467, 13.846, 1, 0.556, 18.453, 0.644, 26.27, 0.733, 28.907, 1, 0.778, 30, 0.822, 30, 0.867, 30, 1, 1.011, 30, 1.156, 28.04, 1.3, 22.85, 1, 1.367, 20.455, 1.433, 18.258, 1.5, 13.85, 1, 1.622, 5.77, 1.744, -3.15, 1.867, -3.15, 1, 1.978, -3.15, 2.089, 4.85, 2.2, 4.85, 1, 2.378, 4.85, 2.556, -16.978, 2.733, -20, 1, 2.878, -22.455, 3.022, -22, 3.167, -22, 1, 3.233, -22, 3.3, -2, 3.367, -2, 1, 3.5, -2, 3.633, -6, 3.767, -6, 1, 3.9, -6, 4.033, -6, 4.167, -6, 1, 4.456, -6, 4.744, -2, 5.033, -2, 0, 7.8, -2]}, {"Target": "Parameter", "Id": "ParamArmRB02Y", "Segments": [0, 0, 1, 0.122, 0, 0.244, 9.78, 0.367, 23, 1, 0.422, 29.009, 0.478, 30, 0.533, 30, 1, 0.644, 30, 0.756, 0, 0.867, 0, 1, 1.1, 0, 1.333, 0, 1.567, 0, 1, 1.756, 0, 1.944, 30, 2.133, 30, 1, 2.167, 30, 2.2, 30, 2.233, 30, 1, 2.456, 30, 2.678, 0, 2.9, 0, 1, 2.944, 0, 2.989, 0, 3.033, 0, 1, 3.144, 0, 3.256, 14.656, 3.367, 14.656, 1, 3.467, 14.656, 3.567, 4.615, 3.667, 4.615, 1, 3.822, 4.615, 3.978, 10.202, 4.133, 10.202, 1, 4.367, 10.202, 4.6, 0, 4.833, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamArmRB03", "Segments": [0, 0, 1, 0.144, 0, 0.289, -19.973, 0.433, -19.973, 1, 0.522, -19.973, 0.611, -18.904, 0.7, -8.205, 1, 0.767, -0.181, 0.833, 18, 0.9, 18, 1, 1.033, 18, 1.167, 8.193, 1.3, -2.506, 1, 1.4, -10.53, 1.5, -11.984, 1.6, -11.984, 1, 1.756, -11.984, 1.911, -6.611, 2.067, -6.611, 1, 2.178, -6.611, 2.289, -6.611, 2.4, -6.611, 1, 2.511, -6.611, 2.622, -3, 2.733, -3, 1, 2.856, -3, 2.978, -3, 3.1, -3, 1, 3.189, -3, 3.278, 11, 3.367, 11, 1, 3.6, 11, 3.833, 11, 4.067, 11, 1, 4.411, 11, 4.756, 20, 5.1, 20, 1, 5.567, 20, 6.033, 17.63, 6.5, 17.63, 0, 7.8, 17.63]}, {"Target": "Parameter", "Id": "ParamHandRB", "Segments": [0, 0, 1, 0.156, 0, 0.311, -10, 0.467, -10, 1, 1.078, -10, 1.689, -10, 2.3, -10, 1, 2.567, -10, 2.833, 10, 3.1, 10, 1, 3.2, 10, 3.3, -10, 3.4, -10, 1, 3.6, -10, 3.8, -10, 4, -10, 1, 4.244, -10, 4.489, 9, 4.733, 9, 1, 5.222, 9, 5.711, 0, 6.2, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAllX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAllY", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAllRotate", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairSideL", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairSideR", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairBackR", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairBackL", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairFrontFuwa", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairSideFuwa", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairBackFuwa", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamWing", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHatBrim", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHatTop", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAccessory1", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAccessory2", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamString", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamRobeL", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamRobeR", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamRobeFuwa", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamSmokeOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamSmoke", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamExplosionChargeOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamExplosionLightCharge", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamExplosionOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamExplosion", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamWandInkColorRainbow", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartMissOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartBackMissOn", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorRainbow", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamWandInkColorHeal", "Segments": [0, 0, 1, 1.044, 0, 2.089, 0, 3.133, 0, 1, 3.244, 0, 3.356, 1, 3.467, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamHeartHealOn", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0.01, 1, 0.444, 0.023, 0.556, 1, 0.667, 1, 1, 1.644, 1, 2.622, 1, 3.6, 1, 1, 3.767, 1, 3.933, 0, 4.1, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartBackHealOn", "Segments": [0, 0, 1, 1, 0, 2, 0, 3, 0, 1, 3.389, 0, 3.778, 1, 4.167, 1, 1, 4.178, 1, 4.189, 0, 4.2, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorHeal", "Segments": [0, 0, 1, 1.078, 0, 2.156, 0, 3.233, 0, 1, 3.544, 0, 3.856, 1, 4.167, 1, 1, 4.178, 1, 4.189, 0, 4.2, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartLightOn", "Segments": [0, 0, 1, 1.089, 0, 2.178, 0, 3.267, 0, 1, 3.567, 0.661, 3.867, 1, 4.167, 1, 1, 4.344, 1, 4.522, 0.757, 4.7, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartLight", "Segments": [0, 0, 1, 1.111, 0, 2.222, 0, 3.333, 0, 1, 3.911, 17.478, 4.489, 30, 5.067, 30, 1, 5.078, 30, 5.089, 0, 5.1, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartLightColor", "Segments": [0, 0, 1, 1.078, 0, 2.156, 0, 3.233, 0, 1, 3.544, 0, 3.856, 1, 4.167, 1, 1, 4.344, 1, 4.522, 1, 4.7, 1, 1, 4.711, 1, 4.722, 0, 4.733, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMagicPositionX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMagicPositionY", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamWandInk", "Segments": [0, 0, 0, 0.5, 0, 0, 2, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamHeartDrow", "Segments": [0, 0, 0, 0.567, 0, 1, 0.8, 13.228, 1.033, 14.173, 1.267, 15, 1, 1.278, 15, 1.289, 15, 1.3, 15, 1, 1.6, 19.829, 1.9, 28.166, 2.2, 30, 1, 2.856, 30, 3.511, 30, 4.167, 30, 1, 4.178, 30, 4.189, 0, 4.2, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartSize", "Segments": [0, 0, 1, 1.089, 0, 2.178, 0, 3.267, 0, 1, 3.367, 0, 3.467, 0.166, 3.567, 0.3, 1, 3.744, 0.537, 3.922, 0.6, 4.1, 0.6, 1, 4.122, 0.6, 4.144, 0.6, 4.167, 0.6, 1, 4.178, 0.6, 4.189, 0, 4.2, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorLight", "Segments": [0, 0, 1, 1.078, 0, 2.156, 0, 3.233, 0, 1, 3.544, 0, 3.856, 1, 4.167, 1, 1, 4.178, 1, 4.189, 0, 4.2, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAllColor", "Segments": [0, 0, 1, 1.456, 0, 2.911, 0, 4.367, 0, 1, 4.611, 0, 4.856, 0.6, 5.1, 0.6, 1, 5.244, 0.6, 5.389, 0.4, 5.533, 0.4, 1, 5.678, 0.4, 5.822, 0.6, 5.967, 0.6, 1, 6.111, 0.6, 6.256, 0.572, 6.4, 0.4, 1, 6.567, 0.201, 6.733, 0, 6.9, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAuraOn", "Segments": [0, 0, 1, 1.278, 0, 2.556, 0, 3.833, 0, 1, 4, 0.957, 4.167, 1, 4.333, 1, 1, 4.833, 1, 5.333, 0.898, 5.833, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAura", "Segments": [0, 0, 1, 1.211, 0, 2.422, 0, 3.633, 0, 1, 4.078, 2.8, 4.522, 8.271, 4.967, 29.268, 2, 5, 0, 1, 5.456, 14.57, 5.911, 29.968, 6.367, 29.268, 0, 6.4, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAuraColor", "Segments": [0, 0, 1, 1.4, 0, 2.8, 0, 4.2, 0, 1, 4.656, 0, 5.111, 1, 5.567, 1, 1, 5.656, 1, 5.744, 1, 5.833, 1, 1, 5.844, 1, 5.856, 0, 5.867, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHealOn", "Segments": [0, 0, 1, 1.244, 0, 2.489, 0, 3.733, 0, 1, 3.989, 0.654, 4.244, 1, 4.5, 1, 1, 4.667, 1, 4.833, 1, 5, 1, 1, 5.5, 1, 6, 0.928, 6.5, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHealLight", "Segments": [0, 0, 1, 1.244, 0, 2.489, 0, 3.733, 0, 1, 4.489, 25.63, 5.244, 30, 6, 30, 1, 6.167, 30, 6.333, 30, 6.5, 30, 1, 6.511, 30, 6.522, 0, 6.533, 0, 0, 7.8, 0]}, {"Target": "PartOpacity", "Id": "PartArmLA", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "PartArmRA", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "PartOpacity", "Id": "PartArmLB", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "PartOpacity", "Id": "PartArmRB", "Segments": [0, 1, 0, 7.8, 1]}]}