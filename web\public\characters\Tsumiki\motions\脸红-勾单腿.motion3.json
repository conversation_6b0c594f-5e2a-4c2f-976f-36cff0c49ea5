{"Version": 3, "Meta": {"Duration": 3, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 32, "TotalSegmentCount": 182, "TotalPointCount": 514, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 10, 0.5, 10, 1, 0.722, 10, 0.944, -10, 1.167, -10, 1, 1.356, -10, 1.544, 9.963, 1.733, 9.963, 1, 1.989, 9.963, 2.244, -10, 2.5, -10, 1, 2.6, -10, 2.7, -5, 2.8, -5, 0, 3, -5]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.589, 0, 0.844, -25, 1.1, -25, 1, 1.233, -25, 1.367, 0, 1.5, 0, 1, 1.622, 0, 1.744, 0, 1.867, 0, 1, 2.022, 0, 2.178, -15, 2.333, -15, 1, 2.389, -15, 2.444, -15, 2.5, -15, 0, 3, -15]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.889, 0, 1.611, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.233, 1, 0.3, 0.957, 0.367, 0.65, 1, 0.4, 0.496, 0.433, 0, 0.467, 0, 1, 0.489, 0, 0.511, 0, 0.533, 0, 1, 0.6, 0, 0.667, 0.65, 0.733, 0.65, 1, 0.8, 0.65, 0.867, 0.65, 0.933, 0.65, 1, 0.967, 0.65, 1, 0, 1.033, 0, 1, 1.056, 0, 1.078, 0, 1.1, 0, 1, 1.178, 0, 1.256, 1, 1.333, 1, 1, 1.478, 1, 1.622, 1, 1.767, 1, 1, 1.8, 1, 1.833, 0, 1.867, 0, 1, 1.889, 0, 1.911, 0, 1.933, 0, 1, 2, 0, 2.067, 0.8, 2.133, 0.8, 1, 2.2, 0.8, 2.267, 0.8, 2.333, 0.8, 1, 2.389, 0.8, 2.444, 0.8, 2.5, 0.8, 0, 3, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 1, 0.367, 1, 1, 0.4, 1, 0.433, 0, 0.467, 0, 1, 0.489, 0, 0.511, 0, 0.533, 0, 1, 0.6, 0, 0.667, 1, 0.733, 1, 1, 0.8, 1, 0.867, 1, 0.933, 1, 1, 0.967, 1, 1, 0, 1.033, 0, 1, 1.056, 0, 1.078, 0, 1.1, 0, 1, 1.178, 0, 1.256, 1, 1.333, 1, 1, 1.478, 1, 1.622, 1, 1.767, 1, 1, 1.8, 1, 1.833, 0, 1.867, 0, 1, 1.889, 0, 1.911, 0, 1.933, 0, 1, 2, 0, 2.067, 1, 2.133, 1, 1, 2.2, 1, 2.267, 1, 2.333, 1, 1, 2.389, 1, 2.444, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.233, 1, 0.3, 0.957, 0.367, 0.65, 1, 0.4, 0.496, 0.433, 0, 0.467, 0, 1, 0.489, 0, 0.511, 0, 0.533, 0, 1, 0.6, 0, 0.667, 0.65, 0.733, 0.65, 1, 0.8, 0.65, 0.867, 0.65, 0.933, 0.65, 1, 0.967, 0.65, 1, 0, 1.033, 0, 1, 1.056, 0, 1.078, 0, 1.1, 0, 1, 1.178, 0, 1.256, 1, 1.333, 1, 1, 1.478, 1, 1.622, 1, 1.767, 1, 1, 1.8, 1, 1.833, 0, 1.867, 0, 1, 1.889, 0, 1.911, 0, 1.933, 0, 1, 2, 0, 2.067, 0.8, 2.133, 0.8, 1, 2.2, 0.8, 2.267, 0.8, 2.333, 0.8, 1, 2.389, 0.8, 2.444, 0.8, 2.5, 0.8, 0, 3, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 1, 0.367, 1, 1, 0.4, 1, 0.433, 0, 0.467, 0, 1, 0.489, 0, 0.511, 0, 0.533, 0, 1, 0.6, 0, 0.667, 1, 0.733, 1, 1, 0.8, 1, 0.867, 1, 0.933, 1, 1, 0.967, 1, 1, 0, 1.033, 0, 1, 1.056, 0, 1.078, 0, 1.1, 0, 1, 1.178, 0, 1.256, 1, 1.333, 1, 1, 1.478, 1, 1.622, 1, 1.767, 1, 1, 1.8, 1, 1.833, 0, 1.867, 0, 1, 1.889, 0, 1.911, 0, 1.933, 0, 1, 2, 0, 2.067, 1, 2.133, 1, 1, 2.2, 1, 2.267, 1, 2.333, 1, 1, 2.389, 1, 2.444, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.267, 0, 0.367, 1, 0.467, 1, 1, 0.656, 1, 0.844, 1, 1.033, 1, 1, 1.133, 1, 1.233, -1, 1.333, -1, 1, 1.478, -1, 1.622, -1, 1.767, -1, 1, 1.889, -1, 2.011, 1, 2.133, 1, 1, 2.2, 1, 2.267, 1, 2.333, 1, 1, 2.389, 1, 2.444, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.056, -0.5, 0.111, -0.5, 0.167, -0.5, 1, 0.267, -0.5, 0.367, -1, 0.467, -1, 1, 0.656, -1, 0.844, -1, 1.033, -1, 1, 1.133, -1, 1.233, 1, 1.333, 1, 1, 1.478, 1, 1.622, 1, 1.767, 1, 1, 1.889, 1, 2.011, -1, 2.133, -1, 1, 2.2, -1, 2.267, -1, 2.333, -1, 1, 2.389, -1, 2.444, -1, 2.5, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.889, 1, 1.611, 1, 2.333, 1, 1, 2.389, 1, 2.444, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.54, 0, 3, 0.54]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.54, 0, 3, 0.54]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.289, 1, 0.411, 0, 0.533, 0, 1, 1.133, 0, 1.733, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.289, 0, 0.411, 0, 0.533, 0, 1, 1.133, 0, 1.733, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamCheek_01", "Segments": [0, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamCheek_02", "Segments": [0, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 5, 0.5, 5, 1, 0.722, 5, 0.944, -3, 1.167, -3, 1, 1.367, -3, 1.567, 6.993, 1.767, 6.993, 1, 1.967, 6.993, 2.167, 0.79, 2.367, 0.79, 1, 2.511, 0.79, 2.656, 1.605, 2.8, 1.605, 0, 3, 1.605]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, -0.046, 0.367, 2, 1, 0.478, 5.409, 0.589, 10, 0.7, 10, 1, 0.789, 10, 0.878, 3, 0.967, 3, 1, 1.233, 3, 1.5, 4.945, 1.767, 4.945, 1, 2.111, 4.945, 2.456, 2, 2.8, 2, 0, 3, 2]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.889, 0, 1.611, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.889, 0, 1.611, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.889, 0, 1.611, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.256, 0, 0.344, 0, 0.433, 0, 1, 0.633, 0, 0.833, 0.7, 1.033, 0.7, 1, 1.2, 0.7, 1.367, 0.2, 1.533, 0.2, 1, 1.856, 0.2, 2.178, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.889, 0, 1.611, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0.313, 0.4, 0.5, 1, 0.567, 0.901, 0.733, 1, 0.9, 1, 1, 1.378, 1, 1.856, 1, 2.333, 1, 1, 2.389, 1, 2.444, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0.313, 0.4, 0.5, 1, 0.567, 0.901, 0.733, 1, 0.9, 1, 1, 1.378, 1, 1.856, 1, 2.333, 1, 1, 2.389, 1, 2.444, 1, 2.5, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 0.333, 0.5, 0.5, 0.5, 0.667, 0.5, 1, 0.744, 0.5, 0.822, 1, 0.9, 1, 1, 1.044, 1, 1.189, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0.213, 1.567, 0.5, 1, 1.667, 0.869, 1.767, 1, 1.867, 1, 1, 2.022, 1, 2.178, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3, 0]}]}