{"Version": 3, "Meta": {"Duration": 2.967, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 31, "TotalSegmentCount": 209, "TotalPointCount": 596, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, 20, 0.733, 20, 1, 0.9, 20, 1.067, 20, 1.233, 20, 1, 1.378, 20, 1.522, 10, 1.667, 10, 1, 1.8, 10, 1.933, 20, 2.067, 20, 1, 2.144, 20, 2.222, 20, 2.3, 20, 1, 2.356, 20, 2.411, 20, 2.467, 20, 0, 2.967, 20]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, 0, 0.733, 0, 1, 0.9, 0, 1.067, 0, 1.233, 0, 1, 1.378, 0, 1.522, -4.619, 1.667, -15, 1, 1.8, -24.582, 1.933, -30, 2.067, -30, 1, 2.144, -30, 2.222, -30, 2.3, -30, 1, 2.356, -30, 2.411, -30, 2.467, -30, 0, 2.967, -30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.633, 0, 1.1, 30, 1.567, 30, 1, 1.811, 30, 2.056, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.244, 1, 0.322, 1, 0.4, 1, 1, 0.456, 1, 0.511, 0, 0.567, 0, 1, 0.578, 0, 0.589, 0, 0.6, 0, 1, 0.689, 0, 0.778, 0.75, 0.867, 0.75, 1, 1.067, 0.75, 1.267, 0.75, 1.467, 0.75, 1, 1.522, 0.75, 1.578, 0, 1.633, 0, 1, 1.644, 0, 1.656, 0, 1.667, 0, 1, 1.744, 0, 1.822, 0.75, 1.9, 0.75, 1, 2.033, 0.75, 2.167, 0.75, 2.3, 0.75, 1, 2.356, 0.75, 2.411, 0.75, 2.467, 0.75, 0, 2.967, 0.75]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.456, 0, 0.511, 0, 0.567, 0, 1, 0.578, 0, 0.589, 0, 0.6, 0, 1, 0.689, 0, 0.778, 1, 0.867, 1, 1, 1.067, 1, 1.267, 1, 1.467, 1, 1, 1.522, 1, 1.578, 0, 1.633, 0, 1, 1.644, 0, 1.656, 0, 1.667, 0, 1, 1.744, 0, 1.822, 1, 1.9, 1, 1, 2.033, 1, 2.167, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.244, 1, 0.322, 1, 0.4, 1, 1, 0.456, 1, 0.511, 0, 0.567, 0, 1, 0.578, 0, 0.589, 0, 0.6, 0, 1, 0.689, 0, 0.778, 0.75, 0.867, 0.75, 1, 1.067, 0.75, 1.267, 0.75, 1.467, 0.75, 1, 1.522, 0.75, 1.578, 0, 1.633, 0, 1, 1.644, 0, 1.656, 0, 1.667, 0, 1, 1.744, 0, 1.822, 0.75, 1.9, 0.75, 1, 2.033, 0.75, 2.167, 0.75, 2.3, 0.75, 1, 2.356, 0.75, 2.411, 0.75, 2.467, 0.75, 0, 2.967, 0.75]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.456, 0, 0.511, 0, 0.567, 0, 1, 0.578, 0, 0.589, 0, 0.6, 0, 1, 0.689, 0, 0.778, 1, 0.867, 1, 1, 1.067, 1, 1.267, 1, 1.467, 1, 1, 1.522, 1, 1.578, 0, 1.633, 0, 1, 1.644, 0, 1.656, 0, 1.667, 0, 1, 1.744, 0, 1.822, 1, 1.9, 1, 1, 2.033, 1, 2.167, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.322, 0, 0.478, 0, 0.633, 0, 1, 0.711, 0, 0.789, -1, 0.867, -1, 1, 1.067, -1, 1.267, -1, 1.467, -1, 1, 1.611, -1, 1.756, 1, 1.9, 1, 1, 2.033, 1, 2.167, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.056, -0.5, 0.111, -0.5, 0.167, -0.5, 1, 0.322, -0.5, 0.478, -0.5, 0.633, -0.5, 1, 0.711, -0.5, 0.789, 0, 0.867, 0, 1, 1.067, 0, 1.267, 0, 1.467, 0, 1, 1.611, 0, 1.756, -1, 1.9, -1, 1, 2.033, -1, 2.167, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 0, 2.967, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.878, 1, 1.589, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, -0.5, 0.9, -0.5, 1, 1.167, -0.5, 1.433, -0.5, 1.7, -0.5, 1, 1.9, -0.5, 2.1, -0.5, 2.3, -0.5, 1, 2.356, -0.5, 2.411, -0.5, 2.467, -0.5, 0, 2.967, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, -0.5, 0.9, -0.5, 1, 1.167, -0.5, 1.433, -0.5, 1.7, -0.5, 1, 1.9, -0.5, 2.1, -0.5, 2.3, -0.5, 1, 2.356, -0.5, 2.411, -0.5, 2.467, -0.5, 0, 2.967, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 0, 0.9, 0, 1, 1.167, 0, 1.433, 0, 1.7, 0, 1, 1.767, 0, 1.833, -0.5, 1.9, -0.5, 1, 2.033, -0.5, 2.167, -0.5, 2.3, -0.5, 1, 2.356, -0.5, 2.411, -0.5, 2.467, -0.5, 0, 2.967, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 0, 0.9, 0, 1, 1.167, 0, 1.433, 0, 1.7, 0, 1, 1.767, 0, 1.833, -0.5, 1.9, -0.5, 1, 2.033, -0.5, 2.167, -0.5, 2.3, -0.5, 1, 2.356, -0.5, 2.411, -0.5, 2.467, -0.5, 0, 2.967, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 0, 0.9, 0, 1, 1.167, 0, 1.433, 0, 1.7, 0, 1, 1.9, 0, 2.1, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 0, 0.9, 0, 1, 1.167, 0, 1.433, 0, 1.7, 0, 1, 1.9, 0, 2.1, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.378, 0, 0.589, -0.6, 0.8, -0.6, 1, 1.3, -0.6, 1.8, -0.6, 2.3, -0.6, 1, 2.356, -0.6, 2.411, -0.6, 2.467, -0.6, 0, 2.967, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.378, 0, 0.589, -0.6, 0.8, -0.6, 1, 1.3, -0.6, 1.8, -0.6, 2.3, -0.6, 1, 2.356, -0.6, 2.411, -0.6, 2.467, -0.6, 0, 2.967, -0.6]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.222, 1, 0.278, 1, 0.333, 1, 1, 0.433, 1, 0.533, 0.315, 0.633, 0, 1, 0.889, -0.804, 1.144, -1, 1.4, -1, 1, 1.7, -1, 2, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 0, 2.967, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.433, 0, 0.533, 0, 0.633, 0, 1, 0.889, 0, 1.144, 0, 1.4, 0, 1, 1.7, 0, 2, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.522, 0, 0.878, 7, 1.233, 7, 1, 1.389, 7, 1.544, 4.019, 1.7, 2, 1, 1.844, 0.125, 1.989, 0, 2.133, 0, 1, 2.189, 0, 2.244, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.333, 0, 0.5, 5, 0.667, 5, 1, 0.8, 5, 0.933, 5, 1.067, 5, 1, 1.4, 5, 1.733, -10, 2.067, -10, 1, 2.144, -10, 2.222, -10, 2.3, -10, 1, 2.356, -10, 2.411, -10, 2.467, -10, 0, 2.967, -10]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 0, 0.9, 0, 1, 1.1, 0, 1.3, -0.2, 1.5, -0.2, 1, 1.678, -0.2, 1.856, 0.545, 2.033, 0.75, 1, 2.256, 1.007, 2.478, 1, 2.7, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.311, 0, 0.456, 0, 0.6, 0, 1, 0.756, 0, 0.911, 0.2, 1.067, 0.2, 1, 1.3, 0.2, 1.533, -0.095, 1.767, -0.655, 1, 1.867, -0.895, 1.967, -1, 2.067, -1, 1, 2.144, -1, 2.222, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 0, 2.967, -1]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.033, 0, 1.9, 0, 2.767, 0, 1, 2.822, 0, 2.878, 0, 2.933, 0, 0, 2.967, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 1, 1, 0.922, 1, 1.844, 1, 2.767, 1, 1, 2.822, 1, 2.878, 1, 2.933, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 1, 1, 0.922, 1, 1.844, 1, 2.767, 1, 1, 2.822, 1, 2.878, 1, 2.933, 1, 0, 2.967, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 0.367, 0.5, 0.567, 0.5, 0.767, 0.5, 1, 0.933, 0.5, 1.1, 1, 1.267, 1, 1, 1.444, 1, 1.622, 0, 1.8, 0, 1, 2.022, 0, 2.244, 0.721, 2.467, 0.721, 1, 2.567, 0.721, 2.667, 0.5, 2.767, 0.5, 1, 2.822, 0.5, 2.878, 0.5, 2.933, 0.5, 0, 2.967, 0.5]}]}