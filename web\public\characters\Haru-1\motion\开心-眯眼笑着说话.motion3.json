{"Version": 3, "Meta": {"Duration": 4.23, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 217, "TotalPointCount": 592, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 9, 1, 0.333, 9, 0.667, 9, 1, 9, 1, 1.056, 9, 1.111, 8.238, 1.167, 6.659, 1, 1.322, 2.238, 1.478, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 1, 2.189, 0, 2.378, -8, 2.567, -8, 1, 2.733, -8, 2.9, 2, 3.067, 2, 0, 4.233, 2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -16, 1, 0.333, -16, 0.667, -16, 1, -16, 1, 1.056, -16, 1.111, -14.645, 1.167, -11.838, 1, 1.322, -3.979, 1.478, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 1, 2.189, 0, 2.378, -15, 2.567, -15, 1, 2.733, -15, 2.9, -10, 3.067, -10, 0, 4.233, -10]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 11, 1, 0.333, 11, 0.667, 11, 1, 11, 1, 1.056, 11, 1.111, 10.068, 1.167, 8.139, 1, 1.322, 2.735, 1.478, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0.516, 2, 0.516, 1, 2.2, 0.516, 2.4, -16, 2.6, -16, 1, 2.767, -16, 2.933, 10, 3.1, 10, 0, 4.233, 10]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 0, 0, 2, 0, 1, 2.078, -0.008, 2.156, 1.008, 2.233, 1, 0, 2.7, 1, 1, 2.744, 1.008, 2.789, -0.008, 2.833, 0, 1, 2.9, -0.008, 2.967, 1.008, 3.033, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.056, 1, 1.111, 1, 1.167, 1, 1, 1.444, 1, 1.722, 1, 2, 1, 1, 2.078, 1, 2.156, 0, 2.233, 0, 1, 2.344, 0, 2.456, 0, 2.567, 0, 1, 2.611, 0, 2.656, 0, 2.7, 0, 1, 2.811, 0, 2.922, 0, 3.033, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 0, 0, 2, 0, 1, 2.078, -0.008, 2.156, 1.008, 2.233, 1, 0, 2.7, 1, 1, 2.744, 1.008, 2.789, -0.008, 2.833, 0, 1, 2.9, -0.008, 2.967, 1.008, 3.033, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.056, 1, 1.111, 1, 1.167, 1, 1, 1.444, 1, 1.722, 1, 2, 1, 1, 2.078, 1, 2.156, 0, 2.233, 0, 1, 2.344, 0, 2.456, 0, 2.567, 0, 1, 2.611, 0, 2.656, 0, 2.7, 0, 1, 2.811, 0, 2.922, 0, 3.033, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.189, 0, 2.378, 0, 2.567, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.44, 1, 0.333, -0.44, 0.667, -0.44, 1, -0.44, 1, 1.056, -0.44, 1.111, -0.44, 1.167, -0.44, 1, 1.444, -0.44, 1.722, -0.272, 2, 0, 1, 2.189, 0.185, 2.378, 0.25, 2.567, 0.25, 1, 2.733, 0.25, 2.9, -0.08, 3.067, -0.08, 0, 4.233, -0.08]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.54, 1, 0.333, 0.54, 0.667, 0.54, 1, 0.54, 1, 1.056, 0.54, 1.111, 0.54, 1.167, 0.54, 1, 1.444, 0.54, 1.722, 0, 2, 0, 1, 2.189, 0, 2.378, 0.58, 2.567, 0.58, 1, 2.733, 0.58, 2.9, 0.58, 3.067, 0.58, 0, 4.233, 0.58]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.178, 0, 2.356, 0, 2.533, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.178, 0, 2.356, 0, 2.533, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.178, 0, 2.356, 0.21, 2.533, 0.21, 1, 2.722, 0.21, 2.911, 0, 3.1, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.178, 0, 2.356, 0.15, 2.533, 0.15, 1, 2.722, 0.15, 2.911, 0, 3.1, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.178, 0, 2.356, 0.48, 2.533, 0.48, 1, 2.722, 0.48, 2.911, 0, 3.1, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 1, 2.178, 0, 2.356, 0.44, 2.533, 0.44, 1, 2.722, 0.44, 2.911, 0, 3.1, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.056, 1, 1.111, 1, 1.167, 1, 1, 1.444, 1, 1.722, 1, 2, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.267, 0, 1, 0.289, 0.01, 0.311, 0.633, 0.333, 0.643, 1, 0.367, 0.592, 0.4, 0.174, 0.433, 0.168, 1, 0.467, 0.079, 0.5, 0.063, 0.533, 0.063, 1, 0.556, 0.076, 0.578, 0.873, 0.6, 0.886, 1, 0.622, 0.874, 0.644, 0.107, 0.667, 0.094, 1, 0.689, 0.103, 0.711, 0.673, 0.733, 0.682, 1, 0.756, 0.672, 0.778, 0.057, 0.8, 0.039, 1, 0.833, 0.027, 0.867, 0.018, 0.9, 0.013, 1, 0.956, 0.003, 1.011, 0, 1.067, 0, 1, 1.089, 0.007, 1.111, 0.456, 1.133, 0.463, 1, 1.156, 0.462, 1.178, 0.457, 1.2, 0.437, 1, 1.233, 0.413, 1.267, 0.324, 1.3, 0.235, 1, 1.356, 0.1, 1.411, 0, 1.467, 0, 1, 1.489, 0.005, 1.511, 0.175, 1.533, 0.41, 1, 1.544, 0.517, 1.556, 0.662, 1.567, 0.777, 1, 1.578, 0.878, 1.589, 1.08, 1.6, 1, 0, 1.733, 1, 1, 1.756, 0.874, 1.778, 0.473, 1.8, 0.267, 1, 1.822, 0.053, 1.844, 0.003, 1.867, 0, 0, 2.267, 0, 1, 2.3, 0.065, 2.333, -0.225, 2.367, 0.871, 3, 2.4, 1, 0, 2.433, 1, 1, 2.467, 0.885, 2.5, 0.037, 2.533, 0.212, 1, 2.578, 0.209, 2.622, 0.536, 2.667, 0.533, 1, 2.689, 0.528, 2.711, 0.217, 2.733, 0.212, 1, 2.789, 0.213, 2.844, 0.661, 2.9, 0.652, 1, 2.933, 0.823, 2.967, 0.374, 3, 0.235, 1, 3.033, 0.235, 3.067, 0.236, 3.1, 0.262, 1, 3.133, 0.069, 3.167, 0.707, 3.2, 0.878, 1, 3.222, 0.866, 3.244, 0.091, 3.267, 0.078, 1, 3.3, 0.268, 3.333, -0.425, 3.367, 0.85, 3, 3.4, 1, 2, 3.433, 0.765, 1, 3.444, 0.403, 3.456, -0.018, 3.467, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 2, 1, 0.333, 2, 0.667, 2, 1, 2, 1, 1.056, 2, 1.111, 1.831, 1.167, 1.48, 1, 1.322, 0.497, 1.478, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.322, 0, 1.478, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -3, 1, 0.333, -3, 0.667, -3, 1, -3, 1, 1.056, -3, 1.111, -2.831, 1.167, -2.48, 1, 1.322, -1.497, 1.478, -1, 1.633, -1, 1, 1.756, -1, 1.878, -1, 2, -1, 1, 2.189, -1, 2.378, -6, 2.567, -6, 1, 2.722, -6, 2.878, 2, 3.033, 2, 0, 4.233, 2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.322, 0, 1.478, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 1.056, 0.5, 1.111, 0.5, 1.167, 0.5, 1, 1.322, 0.5, 1.478, 0.5, 1.633, 0.5, 1, 1.756, 0.5, 1.878, 0.5, 2, 0.5, 0, 4.233, 0.5]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 1.056, 0.5, 1.111, 0.5, 1.167, 0.5, 1, 1.322, 0.5, 1.478, 0.5, 1.633, 0.5, 1, 1.756, 0.5, 1.878, 0.5, 2, 0.5, 0, 4.233, 0.5]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 2.6, 1, 0.333, 2.6, 0.667, 2.6, 1, 2.6, 1, 1.056, 2.6, 1.111, 2.6, 1.167, 2.6, 1, 1.322, 2.6, 1.478, 2.6, 1.633, 2.6, 1, 1.756, 2.6, 1.878, 2.6, 2, 2.6, 1, 2.344, 2.6, 2.689, 0, 3.033, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, -1, 1, 0.333, -1, 0.667, -1, 1, -1, 1, 1.056, -1, 1.111, -1, 1.167, -1, 1, 1.322, -1, 1.478, -1, 1.633, -1, 1, 1.756, -1, 1.878, -1, 2, -1, 1, 2.344, -1, 2.689, 0, 3.033, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0, 1.167, 0, 1, 1.444, 0, 1.722, 0, 2, 0, 0, 4.233, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B_001", "Segments": [0, 1, 0, 4.23, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_B_001", "Segments": [0, 1, 0, 4.23, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A_001", "Segments": [0, 0, 0, 4.23, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A_001", "Segments": [0, 0, 0, 4.23, 0]}]}