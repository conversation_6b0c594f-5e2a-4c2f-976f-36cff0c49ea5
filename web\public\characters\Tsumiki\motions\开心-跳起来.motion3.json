{"Version": 3, "Meta": {"Duration": 3.2, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 191, "TotalPointCount": 597, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 0, 0.5, 0, 1, 0.633, 0, 0.767, -5, 0.9, -5, 1, 0.967, -5, 1.033, 5, 1.1, 5, 1, 1.289, 5, 1.478, 0, 1.667, 0, 1, 1.878, 0, 2.089, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 0, 0.567, 0, 1, 0.678, 0, 0.789, -10, 0.9, -10, 1, 0.967, -10, 1.033, 30, 1.1, 30, 1, 1.289, 30, 1.478, -3.506, 1.667, -3.506, 1, 1.756, -3.506, 1.844, 0, 1.933, 0, 1, 2.056, 0, 2.178, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.322, 1, 0.478, 1, 0.633, 1, 1, 0.678, 1, 0.722, 0, 0.767, 0, 1, 0.8, 0, 0.833, 0, 0.867, 0, 1, 0.933, 0, 1, 2, 1.067, 2, 1, 1.144, 2, 1.222, 2, 1.3, 2, 1, 1.356, 2, 1.411, 0, 1.467, 0, 1, 1.556, 0, 1.644, 0, 1.733, 0, 1, 1.811, 0, 1.889, 1, 1.967, 1, 1, 2.078, 1, 2.189, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 1, 3.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.322, 0, 0.478, 0, 0.633, 0, 1, 0.678, 0, 0.722, 0.75, 0.767, 0.75, 1, 0.8, 0.75, 0.833, 0.75, 0.867, 0.75, 1, 0.933, 0.75, 1, 0, 1.067, 0, 1, 1.144, 0, 1.222, 0, 1.3, 0, 1, 1.356, 0, 1.411, 1, 1.467, 1, 1, 1.556, 1, 1.644, 1, 1.733, 1, 1, 1.811, 1, 1.889, 0, 1.967, 0, 1, 2.078, 0, 2.189, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.322, 1, 0.478, 1, 0.633, 1, 1, 0.678, 1, 0.722, 0, 0.767, 0, 1, 0.8, 0, 0.833, 0, 0.867, 0, 1, 0.933, 0, 1, 2, 1.067, 2, 1, 1.144, 2, 1.222, 2, 1.3, 2, 1, 1.356, 2, 1.411, 0, 1.467, 0, 1, 1.556, 0, 1.644, 0, 1.733, 0, 1, 1.811, 0, 1.889, 1, 1.967, 1, 1, 2.078, 1, 2.189, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 1, 3.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.322, 0, 0.478, 0, 0.633, 0, 1, 0.678, 0, 0.722, 0.75, 0.767, 0.75, 1, 0.8, 0.75, 0.833, 0.75, 0.867, 0.75, 1, 0.933, 0.75, 1, 0, 1.067, 0, 1, 1.144, 0, 1.222, 0, 1.3, 0, 1, 1.356, 0, 1.411, 1, 1.467, 1, 1, 1.556, 1, 1.644, 1, 1.733, 1, 1, 1.811, 1, 1.889, 0, 1.967, 0, 1, 2.078, 0, 2.189, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.578, -0.5, 1.156, -0.5, 1.733, -0.5, 1, 1.922, -0.5, 2.111, -0.5, 2.3, -0.5, 1, 2.356, -0.5, 2.411, -0.5, 2.467, -0.5, 1, 2.711, -0.5, 2.956, -0.5, 3.2, -0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.767, 1, 1.533, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 1, 3.2, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 1, 1, 0.767, 1, 1.533, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 0.99, 3.2, 0.973]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 1, 1, 0.767, 1, 1.533, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 0.99, 3.2, 0.973]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.411, 1, 0.656, 1, 0.9, 1, 1, 0.989, 1, 1.078, 1, 1.167, 1, 1, 1.433, 1, 1.7, 1, 1.967, 1, 1, 2.078, 1, 2.189, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 1, 3.2, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 0, 0.9, 0, 1, 0.989, 0, 1.078, 1, 1.167, 1, 1, 1.267, 1, 1.367, 0, 1.467, 0, 1, 1.556, 0, 1.644, 0, 1.733, 0, 1, 1.811, 0, 1.889, 0.65, 1.967, 0.65, 1, 2.078, 0.65, 2.189, 0.65, 2.3, 0.65, 1, 2.356, 0.65, 2.411, 0.65, 2.467, 0.65, 1, 2.711, 0.65, 2.956, 0.644, 3.2, 0.633]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 0, 0.567, 0, 1, 0.722, 0, 0.878, -5, 1.033, -5, 1, 1.244, -5, 1.456, 0, 1.667, 0, 1, 1.878, 0, 2.089, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 0, 0.567, 0, 1, 0.678, 0, 0.789, -9, 0.9, -9, 1, 0.967, -9, 1.033, 10, 1.1, 10, 1, 1.289, 10, 1.478, -9, 1.667, -9, 1, 1.878, -9, 2.089, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 0, 0.567, 0, 1, 0.678, 0, 0.789, -3, 0.9, -3, 1, 1.156, -3, 1.411, -1.196, 1.667, 0, 1, 1.878, 0.988, 2.089, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.711, 1, 2.956, 0.99, 3.2, 0.973]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamTie", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.633, 0, 0.767, 1, 0.9, 1, 1, 1, 1, 1.1, -1, 1.2, -1, 1, 1.378, -1, 1.556, 0.4, 1.733, 0.4, 1, 1.922, 0.4, 2.111, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 0, 0.567, 0, 1, 0.689, 0, 0.811, -0.75, 0.933, -0.75, 1, 1.067, -0.75, 1.2, 1, 1.333, 1, 1, 1.478, 1, 1.622, -0.71, 1.767, -0.71, 1, 1.944, -0.71, 2.122, 0, 2.3, 0, 1, 2.6, 0, 2.9, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamRibonL", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamRibonR", "Segments": [0, 0, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 0, 0.567, 0, 1, 0.678, 0, 0.789, -0.8, 0.9, -0.8, 1, 0.989, -0.8, 1.078, 1, 1.167, 1, 1, 1.356, 1, 1.544, 0.4, 1.733, 0.4, 1, 1.833, 0.4, 1.933, 0.75, 2.033, 0.75, 1, 2.122, 0.75, 2.211, 0.75, 2.3, 0.75, 1, 2.356, 0.75, 2.411, 0.75, 2.467, 0.75, 1, 2.711, 0.75, 2.956, 0.743, 3.2, 0.73]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.711, 0, 2.956, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 0.878, 0.5, 1.589, 0.5, 2.3, 0.5, 1, 2.356, 0.5, 2.411, 0.5, 2.467, 0.5, 1, 2.711, 0.5, 2.956, 0.5, 3.2, 0.5]}]}