# -*- coding: utf-8 -*-
'''
@File    :   funASRAPI.py
<AUTHOR>   一力辉 
'''

"""
FunASR WebSocket Client API
Based on the funasr_wss_client.py example
"""

from ..builder import ASREngines
from ..engineBase import BaseEngine
import websockets
import ssl
import json
import async<PERSON>
from typing import List, Optional
from digitalHuman.utils import AudioMessage, TextMessage
from digitalHuman.utils import logger

__all__ = ["FunASRAPI"]

@ASREngines.register("FunASRAPI")
class FunASRAPI(BaseEngine): 
    def checkKeys(self) -> List[str]:
        return ["HOST", "PORT", "SSL_ENABLED", "MODE", "CHUNK_SIZE", "CHUNK_INTERVAL", "USE_ITN"]
    
    def setup(self):
        self.websocket = None
        self.result_text = ""
        self.is_final = False
    
    async def connect_websocket(self):
        if self.cfg.SSL_ENABLED:
            ssl_context = ssl.SSLContext()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            uri = f"wss://{self.cfg.HOST}:{self.cfg.PORT}"
            ssl_context_param = ssl_context
        else:
            uri = f"ws://{self.cfg.HOST}:{self.cfg.PORT}"
            ssl_context_param = None
        
        logger.debug(f"[ASR] Connecting to FunASR server: {uri}")
        self.websocket = await websockets.connect(
            uri, 
            subprotocols=["binary"], 
            ping_interval=None, 
            ssl=ssl_context_param
        )
        return self.websocket
    
    async def run(self, input: AudioMessage, **kwargs) -> Optional[TextMessage]:
        try:
            # Parse chunk_size from config
            chunk_size = [int(x) for x in self.cfg.CHUNK_SIZE.split(",")]
            
            # Connect to websocket if not already connected
            if self.websocket is None or self.websocket.closed:
                self.websocket = await self.connect_websocket()
            
            # Reset result state
            self.result_text = ""
            self.is_final = False
            
            # Prepare initial message with configuration
            use_itn = self.cfg.USE_ITN
            mode = self.cfg.MODE
            
            # Send initial configuration message
            init_message = json.dumps({
                "mode": mode, 
                "chunk_size": chunk_size, 
                "chunk_interval": self.cfg.CHUNK_INTERVAL,
                "audio_fs": input.sampleRate,
                "wav_name": "digitalHuman", 
                "wav_format": str(input.format), 
                "is_speaking": True, 
                "hotwords": "", 
                "itn": use_itn
            })
            
            await self.websocket.send(init_message)
            
            # Send audio data
            await self.websocket.send(input.data)
            
            # Send end of speaking message
            end_message = json.dumps({"is_speaking": False})
            await self.websocket.send(end_message)
            
            # Create a task to receive messages
            receive_task = asyncio.create_task(self.receive_messages())
            
            # Wait for the result with timeout
            try:
                await asyncio.wait_for(receive_task, timeout=10.0)
            except asyncio.TimeoutError:
                logger.warning("[ASR] FunASR recognition timed out")
            
            # Return the recognized text
            message = TextMessage(data=self.result_text)
            return message
            
        except Exception as e:
            logger.error(f"[ASR] Engine run failed: {e}", exc_info=True)
            # Close websocket on error
            if self.websocket and not self.websocket.closed:
                await self.websocket.close()
                self.websocket = None
            return None
    
    async def receive_messages(self):
        """Receive and process messages from the FunASR server"""
        while not self.is_final:
            try:
                response = await self.websocket.recv()
                response_json = json.loads(response)
                
                # Extract text from response
                if "text" in response_json:
                    text = response_json["text"]
                    self.result_text = text
                    
                    # Check if this is the final result
                    self.is_final = response_json.get("is_final", False)
                    
                    # For offline mode, we consider any response as final
                    if self.cfg.MODE == "offline":
                        self.is_final = True
                        
                    logger.debug(f"[ASR] Received text: {text}, is_final: {self.is_final}")
            except Exception as e:
                logger.error(f"[ASR] Error receiving message: {e}", exc_info=True)
                break
