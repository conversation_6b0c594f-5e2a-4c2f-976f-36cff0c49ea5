{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.0.4", "@heroicons/react": "^2.1.3", "@nextui-org/button": "2.0.34", "@nextui-org/card": "^2.0.33", "@nextui-org/code": "2.0.29", "@nextui-org/input": "2.2.2", "@nextui-org/kbd": "2.0.30", "@nextui-org/link": "2.0.32", "@nextui-org/listbox": "2.1.21", "@nextui-org/modal": "^2.0.36", "@nextui-org/navbar": "2.0.33", "@nextui-org/radio": "^2.1.2", "@nextui-org/react": "^2.6.11", "@nextui-org/snippet": "2.0.38", "@nextui-org/switch": "2.0.31", "@nextui-org/system": "2.2.1", "@nextui-org/table": "^2.0.36", "@nextui-org/theme": "^2.2.5", "clsx": "^2.1.1", "eruda": "^3.3.0", "framer-motion": "^12.11.0", "js-audio-recorder": "^1.0.7", "lodash": "^4.17.21", "next": "^14.2.29", "or": "^0.2.0", "react": "^18", "react-dom": "^18", "react-markdown": "^9.0.1", "react-use": "^17.5.1", "react-use-websocket": "^4.8.1", "websocket": "^1.0.35", "whatwg-fetch": "^3.6.20", "zustand": "^4.5.2"}, "overrides": {"@tanstack/react-virtual": "3.9.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}