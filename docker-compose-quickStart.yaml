version: '3'
services:
  # awesome digital human web
  adh_web:
    image: "registry.cn-hangzhou.aliyuncs.com/awesome-digital-human/adh-web:main-latest"
    restart: always
    pull_policy: always
    # network_mode: host
    ports:
      - "3000:3000"
    volumes:
      - ./web/.env:/workspace/.env
  # awesome digital human server
  adh_server:
    image: "registry.cn-hangzhou.aliyuncs.com/awesome-digital-human/adh-api:main-latest"
    restart: always
    pull_policy: always
    # network_mode: host
    ports:
      - "8000:8000"
    volumes:
      - ./configs:/workspace/configs
