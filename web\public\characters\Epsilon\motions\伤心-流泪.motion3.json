{"Version": 3, "Meta": {"Duration": 1.53, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 97, "TotalPointCount": 253, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.411, 0, 0.489, 13, 0.567, 13, 1, 0.644, 13, 0.722, 7, 0.8, 7, 0, 1.533, 7]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 30, 0.333, 30, 1, 0.411, 30, 0.489, -18, 0.567, -18, 1, 0.644, -18, 0.722, 0, 0.8, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 17, 1, 0.089, 17, 0.178, -6, 0.267, -6, 1, 0.4, -6, 0.533, 13, 0.667, 13, 0, 1.533, 13]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1.5, 1, 0.089, 1.5, 0.178, 1.5, 0.267, 1.5, 1, 0.311, 1.5, 0.356, 1.5, 0.4, 1.5, 1, 0.444, 1.5, 0.489, 0, 0.533, 0, 1, 0.556, 0, 0.578, 0, 0.6, 0, 1, 0.656, 0, 0.711, 1, 0.767, 1, 0, 1.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.311, 1, 0.356, 1, 0.4, 1, 1, 0.522, 1, 0.644, 1, 0.767, 1, 0, 1.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1.5, 1, 0.089, 1.5, 0.178, 1.5, 0.267, 1.5, 1, 0.311, 1.5, 0.356, 1.5, 0.4, 1.5, 1, 0.444, 1.5, 0.489, 0, 0.533, 0, 1, 0.556, 0, 0.578, 0, 0.6, 0, 1, 0.656, 0, 0.711, 1, 0.767, 1, 0, 1.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.389, 1, 0.511, 1, 0.633, 1, 0, 1.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.1, 0, 0.2, -0.14, 0.3, -0.14, 1, 0.422, -0.14, 0.544, 0.28, 0.667, 0.28, 1, 0.711, 0.28, 0.756, -0.31, 0.8, -0.31, 0, 1.533, -0.31]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -1, 1, 0.1, -1, 0.2, -1.012, 0.3, -0.92, 1, 0.422, -0.807, 0.544, -0.382, 0.667, -0.08, 1, 0.711, 0.03, 0.756, 0.13, 0.8, 0.13, 0, 1.533, 0.13]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.4, 1, 0.533, 0, 0.667, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.4, 1, 0.533, 0, 0.667, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.28, 1, 0.089, 0.28, 0.178, 0.28, 0.267, 0.28, 1, 0.4, 0.28, 0.533, 0.28, 0.667, 0.28, 0, 1.533, 0.28]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.31, 1, 0.089, 0.31, 0.178, 0.31, 0.267, 0.31, 1, 0.4, 0.31, 0.533, 0.33, 0.667, 0.33, 0, 1.533, 0.33]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.4, 1, 0.533, -0.83, 0.667, -0.83, 0, 1.533, -0.83]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.4, 1, 0.533, -0.86, 0.667, -0.86, 0, 1.533, -0.86]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.089, -1, 0.178, -1, 0.267, -1, 1, 0.4, -1, 0.533, -0.33, 0.667, -0.33, 0, 1.533, -0.33]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.4, 0, 0.533, 0, 0.667, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 1, 1, 0.133, 1, 0.267, 0, 0.4, 0, 1, 0.489, 0, 0.578, 1, 0.667, 1, 0, 1.533, 1]}, {"Target": "Parameter", "Id": "ParamRage", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.089, 0, 0.178, 4, 0.267, 4, 1, 0.4, 4, 0.533, 0, 0.667, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.089, 0, 0.178, -5, 0.267, -5, 1, 0.456, -5, 0.644, -2, 0.833, -2, 0, 1.533, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 10, 0.267, 10, 1, 0.356, 10, 0.444, -3, 0.533, -3, 1, 0.6, -3, 0.667, 0, 0.733, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.467, 0, 0.633, -10, 0.8, -10, 0, 1.533, -10]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.467, 0, 0.633, -10, 0.8, -10, 0, 1.533, -10]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_L", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_R", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_L", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_R", "Segments": [0, 0, 0, 1.533, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_FACE_001_c", "Segments": [0, 0, 0, 1.53, 0]}]}