import React from 'react';

export const SimpleImage = ({ 
  src, 
  alt = '', 
  width = '100%', 
  height = 'auto',
  radius = 'none',
  shadow = 'none',
  isZoomed = false,
  className = '',
  style = {},
  ...props 
}) => {
  const imageStyles = {
    width,
    height,
    borderRadius: radius === 'lg' ? '0.5rem' : radius === 'md' ? '0.375rem' : radius === 'sm' ? '0.25rem' : radius,
    boxShadow: shadow === 'sm' ? '0 1px 2px 0 rgba(0, 0, 0, 0.05)' : 
               shadow === 'md' ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' : 
               shadow === 'lg' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 
               shadow === 'xl' ? '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' : 
               shadow === '2xl' ? '0 25px 50px -12px rgba(0, 0, 0, 0.25)' : 'none',
    transition: isZoomed ? 'transform 0.3s ease' : 'none',
    ...style
  };

  const containerStyles = {
    overflow: 'hidden',
    display: 'inline-block',
    borderRadius: radius === 'lg' ? '0.5rem' : radius === 'md' ? '0.375rem' : radius === 'sm' ? '0.25rem' : radius,
  };

  const handleMouseEnter = (e) => {
    if (isZoomed) {
      e.currentTarget.querySelector('img').style.transform = 'scale(1.1)';
    }
  };

  const handleMouseLeave = (e) => {
    if (isZoomed) {
      e.currentTarget.querySelector('img').style.transform = 'scale(1)';
    }
  };

  return (
    <div 
      className={className} 
      style={containerStyles}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <img 
        src={src} 
        alt={alt} 
        style={imageStyles} 
        {...props} 
      />
    </div>
  );
};

export default SimpleImage;
