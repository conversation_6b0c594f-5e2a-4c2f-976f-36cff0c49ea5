import React, { useState, useEffect } from 'react';

export const SimpleRadioGroup = ({ 
  children, 
  label,
  defaultValue,
  onChange,
  orientation = 'vertical',
  className = '', 
  ...props 
}) => {
  const [selectedValue, setSelectedValue] = useState(defaultValue);
  
  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);
  
  const handleChange = (e) => {
    setSelectedValue(e.target.value);
    if (onChange) {
      onChange(e);
    }
  };
  
  const containerStyles = {
    display: 'flex',
    flexDirection: 'column',
    gap: '0.5rem',
  };
  
  const labelStyles = {
    fontWeight: 'medium',
    marginBottom: '0.5rem',
  };
  
  const radioGroupStyles = {
    display: 'flex',
    flexDirection: orientation === 'horizontal' ? 'row' : 'column',
    gap: orientation === 'horizontal' ? '1rem' : '0.5rem',
  };
  
  // Clone children and pass necessary props
  const radioButtons = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === SimpleRadio) {
      return React.cloneElement(child, {
        checked: child.props.value === selectedValue,
        onChange: handleChange,
        name: label,
      });
    }
    return child;
  });
  
  return (
    <div style={containerStyles} className={className} {...props}>
      {label && <div style={labelStyles}>{label}</div>}
      <div style={radioGroupStyles}>
        {radioButtons}
      </div>
    </div>
  );
};

export const SimpleRadio = ({ 
  children, 
  value,
  checked,
  onChange,
  name,
  className = '', 
  ...props 
}) => {
  const containerStyles = {
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    cursor: 'pointer',
  };
  
  const radioStyles = {
    appearance: 'none',
    width: '1rem',
    height: '1rem',
    borderRadius: '50%',
    border: '2px solid #cbd5e0',
    outline: 'none',
    cursor: 'pointer',
    position: 'relative',
    ':checked': {
      borderColor: '#3182ce',
    },
    ':checked::after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: '0.5rem',
      height: '0.5rem',
      borderRadius: '50%',
      backgroundColor: '#3182ce',
    },
  };
  
  return (
    <label style={containerStyles} className={className}>
      <input
        type="radio"
        value={value}
        checked={checked}
        onChange={onChange}
        name={name}
        style={radioStyles}
        {...props}
      />
      {children}
    </label>
  );
};

export default SimpleRadioGroup;
