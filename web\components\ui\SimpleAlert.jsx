import React from 'react';

const alertStyles = {
  base: {
    padding: '16px',
    borderRadius: '8px',
    marginBottom: '16px',
    display: 'flex',
    alignItems: 'center',
  },
  info: {
    backgroundColor: '#e0f7fa',
    color: '#006064',
    border: '1px solid #00acc1',
  },
  success: {
    backgroundColor: '#e8f5e9',
    color: '#1b5e20',
    border: '1px solid #43a047',
  },
  warning: {
    backgroundColor: '#fff8e1',
    color: '#ff6f00',
    border: '1px solid #ffb300',
  },
  error: {
    backgroundColor: '#ffebee',
    color: '#b71c1c',
    border: '1px solid #e53935',
  }
};

export const SimpleAlert = ({ 
  children, 
  type = 'info', 
  className = '', 
  ...props 
}) => {
  const style = {
    ...alertStyles.base,
    ...alertStyles[type]
  };
  
  return (
    <div style={style} className={className} {...props}>
      {children}
    </div>
  );
};

export default SimpleAlert;
