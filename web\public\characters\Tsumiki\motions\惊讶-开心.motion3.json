{"Version": 3, "Meta": {"Duration": 25.133, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 46, "TotalSegmentCount": 836, "TotalPointCount": 2460, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 1.922, 0, 2.311, 0, 2.7, 0, 1, 2.811, 0, 2.922, 0, 3.033, 0, 1, 3.111, 0, 3.189, 0, 3.267, 0, 1, 3.344, 0, 3.422, 0, 3.5, 0, 1, 4.289, 0, 5.078, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.556, 0, 7.778, 0, 8, 0, 1, 8.2, 0, 8.4, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, 0, 16.2, 0, 1, 17.044, 0, 17.889, 0, 18.733, 0, 1, 18.844, 0, 18.956, 0, 19.067, 0, 1, 19.644, 0, 20.222, 0, 20.8, 0, 1, 21.656, 0, 22.511, 0, 23.367, 0, 1, 23.611, 0, 23.856, 0, 24.1, 0, 1, 24.267, 0, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 1.922, 0, 2.311, 0, 2.7, 0, 1, 2.811, 0, 2.922, 16, 3.033, 16, 1, 3.111, 16, 3.189, -11, 3.267, -11, 1, 3.344, -11, 3.422, 0, 3.5, 0, 1, 4.289, 0, 5.078, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.556, 0, 7.778, -30, 8, -30, 1, 8.2, -30, 8.4, -30, 8.6, -30, 1, 8.878, -30, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, -30, 16.2, -30, 1, 17.044, -30, 17.889, -30, 18.733, -30, 1, 18.844, -30, 18.956, -30, 19.067, -30, 1, 19.311, -30, 19.556, -21.102, 19.8, -16, 1, 20.133, -9.042, 20.467, -6.88, 20.8, 0, 1, 21.656, 17.659, 22.511, 30, 23.367, 30, 1, 23.611, 30, 23.856, 30, 24.1, 30, 1, 24.267, 30, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, -7, 1, -7, 1, 2.622, -7, 4.244, -5.001, 5.867, -5.001, 1, 5.956, -5.001, 6.044, -5.001, 6.133, -5.001, 1, 7.233, -5.001, 8.333, -4.564, 9.433, 0, 1, 10, 2.351, 10.567, 13, 11.133, 13, 1, 11.489, 13, 11.844, -15, 12.2, -15, 1, 12.456, -15, 12.711, 15, 12.967, 15, 1, 13.678, 15, 14.389, 15, 15.1, 15, 1, 15.467, 15, 15.833, -15, 16.2, -15, 1, 17.044, -15, 17.889, -15, 18.733, -15, 1, 18.844, -15, 18.956, -15, 19.067, -15, 1, 19.311, -15, 19.556, -30, 19.8, -30, 1, 20.133, -30, 20.467, 0, 20.8, 0, 1, 22.067, 0, 23.333, -15, 24.6, -15, 1, 24.689, -15, 24.778, -15, 24.867, -15, 1, 24.944, -15, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.178, 1, 1.356, 1, 1.533, 1, 1, 1.8, 1, 2.067, 1, 2.333, 1, 1, 2.567, 1, 2.8, 1, 3.033, 1, 1, 3.111, 1, 3.189, 0, 3.267, 0, 1, 3.344, 0, 3.422, 0, 3.5, 0, 1, 3.633, 0, 3.767, 1, 3.9, 1, 1, 4.133, 1, 4.367, 1, 4.6, 1, 1, 4.744, 1, 4.889, 1, 5.033, 1, 1, 5.311, 1, 5.589, 1, 5.867, 1, 1, 5.956, 1, 6.044, 1, 6.133, 1, 1, 6.533, 1, 6.933, 1, 7.333, 1, 1, 7.556, 1, 7.778, 0, 8, 0, 1, 8.2, 0, 8.4, 0, 8.6, 0, 1, 8.878, 0, 9.156, 1, 9.433, 1, 1, 10.533, 1, 11.633, 1, 12.733, 1, 1, 12.978, 1, 13.222, 1.2, 13.467, 1.2, 1, 13.733, 1.2, 14, 1.2, 14.267, 1.2, 1, 14.3, 1.2, 14.333, 0, 14.367, 0, 1, 14.411, 0, 14.456, 1.2, 14.5, 1.2, 1, 14.544, 1.2, 14.589, 0, 14.633, 0, 1, 14.667, 0, 14.7, 1.2, 14.733, 1.2, 1, 14.856, 1.2, 14.978, 1.2, 15.1, 1.2, 1, 15.467, 1.2, 15.833, 0.853, 16.2, 0.8, 1, 17.156, 0.661, 18.111, 0.64, 19.067, 0.64, 1, 19.311, 0.64, 19.556, 0.737, 19.8, 0.8, 1, 20.133, 0.886, 20.467, 0.912, 20.8, 1, 1, 21.144, 1.091, 21.489, 1.2, 21.833, 1.2, 1, 21.867, 1.2, 21.9, 0, 21.933, 0, 1, 21.978, 0, 22.022, 1.2, 22.067, 1.2, 1, 22.111, 1.2, 22.156, 0, 22.2, 0, 1, 22.233, 0, 22.267, 1.2, 22.3, 1.2, 1, 22.433, 1.2, 22.567, 1.2, 22.7, 1.2, 1, 22.789, 1.2, 22.878, 1.2, 22.967, 1.2, 1, 23.1, 1.2, 23.233, 0.8, 23.367, 0.8, 1, 23.611, 0.8, 23.856, 0.8, 24.1, 0.8, 1, 24.267, 0.8, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 1, 25.1, 1, 0, 25.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.756, 0, 2.511, 1, 3.267, 1, 1, 3.344, 1, 3.422, 1, 3.5, 1, 1, 3.633, 1, 3.767, 0, 3.9, 0, 1, 4.133, 0, 4.367, 0, 4.6, 0, 1, 4.744, 0, 4.889, 0, 5.033, 0, 1, 5.311, 0, 5.589, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 17.344, 0, 19.589, 0, 21.833, 0, 1, 21.989, 0, 22.144, 0, 22.3, 0, 1, 23.067, 0, 23.833, 1, 24.6, 1, 1, 24.689, 1, 24.778, 1, 24.867, 1, 1, 24.944, 1, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.178, 1, 1.356, 1, 1.533, 1, 1, 1.8, 1, 2.067, 1, 2.333, 1, 1, 2.567, 1, 2.8, 1, 3.033, 1, 1, 3.111, 1, 3.189, 0, 3.267, 0, 1, 3.344, 0, 3.422, 0, 3.5, 0, 1, 3.633, 0, 3.767, 1, 3.9, 1, 1, 4.133, 1, 4.367, 1, 4.6, 1, 1, 4.744, 1, 4.889, 1, 5.033, 1, 1, 5.311, 1, 5.589, 1, 5.867, 1, 1, 5.956, 1, 6.044, 1, 6.133, 1, 1, 6.533, 1, 6.933, 1, 7.333, 1, 1, 7.556, 1, 7.778, 0, 8, 0, 1, 8.2, 0, 8.4, 0, 8.6, 0, 1, 8.878, 0, 9.156, 1, 9.433, 1, 1, 10.533, 1, 11.633, 1, 12.733, 1, 1, 12.978, 1, 13.222, 1.2, 13.467, 1.2, 1, 13.733, 1.2, 14, 1.2, 14.267, 1.2, 1, 14.3, 1.2, 14.333, 0, 14.367, 0, 1, 14.411, 0, 14.456, 1.2, 14.5, 1.2, 1, 14.544, 1.2, 14.589, 0, 14.633, 0, 1, 14.667, 0, 14.7, 1.2, 14.733, 1.2, 1, 14.856, 1.2, 14.978, 1.2, 15.1, 1.2, 1, 15.467, 1.2, 15.833, 0.853, 16.2, 0.8, 1, 17.156, 0.661, 18.111, 0.64, 19.067, 0.64, 1, 19.311, 0.64, 19.556, 0.737, 19.8, 0.8, 1, 20.133, 0.886, 20.467, 0.912, 20.8, 1, 1, 21.144, 1.091, 21.489, 1.2, 21.833, 1.2, 1, 21.867, 1.2, 21.9, 0, 21.933, 0, 1, 21.978, 0, 22.022, 1.2, 22.067, 1.2, 1, 22.111, 1.2, 22.156, 0, 22.2, 0, 1, 22.233, 0, 22.267, 1.2, 22.3, 1.2, 1, 22.433, 1.2, 22.567, 1.2, 22.7, 1.2, 1, 22.789, 1.2, 22.878, 1.2, 22.967, 1.2, 1, 23.1, 1.2, 23.233, 0.8, 23.367, 0.8, 1, 23.611, 0.8, 23.856, 0.8, 24.1, 0.8, 1, 24.267, 0.8, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 1, 25.1, 1, 0, 25.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.756, 0, 2.511, 1, 3.267, 1, 1, 3.344, 1, 3.422, 1, 3.5, 1, 1, 3.633, 1, 3.767, 0, 3.9, 0, 1, 4.133, 0, 4.367, 0, 4.6, 0, 1, 4.744, 0, 4.889, 0, 5.033, 0, 1, 5.311, 0, 5.589, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 17.344, 0, 19.589, 0, 21.833, 0, 1, 21.989, 0, 22.144, 0, 22.3, 0, 1, 23.067, 0, 23.833, 1, 24.6, 1, 1, 24.689, 1, 24.778, 1, 24.867, 1, 1, 24.944, 1, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.967, 0, 2.933, 0, 3.9, 0, 1, 4.133, 0, 4.367, -1, 4.6, -1, 1, 4.744, -1, 4.889, -1, 5.033, -1, 1, 5.311, -1, 5.589, -1, 5.867, -1, 1, 5.956, -1, 6.044, -1, 6.133, -1, 1, 6.956, -1, 7.778, -1, 8.6, -1, 1, 8.878, -1, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, 1, 16.2, 1, 1, 18.456, 1, 20.711, 1, 22.967, 1, 1, 23.1, 1, 23.233, -1, 23.367, -1, 1, 23.611, -1, 23.856, -1, 24.1, -1, 1, 24.267, -1, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.756, 0, 7.378, 0, 8, 0, 1, 8.2, 0, 8.4, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.33, 1, 0.33, 1, 1.178, 0.33, 1.356, 0, 1.533, 0, 1, 1.8, 0, 2.067, 0, 2.333, 0, 1, 2.567, 0, 2.8, 0.18, 3.033, 0.18, 1, 3.322, 0.18, 3.611, 0.18, 3.9, 0.18, 1, 4.133, 0.18, 4.367, 0.18, 4.6, 0.18, 1, 4.744, 0.18, 4.889, 0.18, 5.033, 0.18, 1, 5.311, 0.18, 5.589, 0.144, 5.867, 0.144, 1, 5.956, 0.144, 6.044, 0.144, 6.133, 0.144, 1, 6.756, 0.144, 7.378, 0, 8, 0, 1, 8.2, 0, 8.4, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 0.35, 10.667, 0.35, 1, 10.989, 0.35, 11.311, 0.35, 11.633, 0.35, 1, 12.322, 0.35, 13.011, 0.34, 13.7, 0.34, 1, 14.167, 0.34, 14.633, 0.34, 15.1, 0.34, 1, 16.311, 0.34, 17.522, 0.151, 18.733, -0.34, 1, 18.844, -0.385, 18.956, -1, 19.067, -1, 1, 19.311, -1, 19.556, 0.39, 19.8, 0.39, 1, 20.133, 0.39, 20.467, 0, 20.8, 0, 1, 21.522, 0, 22.244, 0, 22.967, 0, 1, 23.1, 0, 23.233, -0.87, 23.367, -0.87, 1, 23.611, -0.87, 23.856, -0.87, 24.1, -0.87, 1, 24.267, -0.87, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 2.322, 0, 3.111, -0.53, 3.9, -0.53, 1, 4.133, -0.53, 4.367, -0.53, 4.6, -0.53, 1, 4.744, -0.53, 4.889, -0.53, 5.033, -0.53, 1, 5.311, -0.53, 5.589, -0.623, 5.867, -0.623, 1, 5.956, -0.623, 6.044, -0.623, 6.133, -0.623, 1, 6.756, -0.623, 7.378, -1, 8, -1, 1, 8.2, -1, 8.4, -1, 8.6, -1, 1, 8.878, -1, 9.156, -0.682, 9.433, -0.5, 1, 9.844, -0.23, 10.256, -0.19, 10.667, -0.19, 1, 10.989, -0.19, 11.311, -0.19, 11.633, -0.19, 1, 12.322, -0.19, 13.011, 0, 13.7, 0, 1, 14.167, 0, 14.633, 0, 15.1, 0, 1, 16.311, 0, 17.522, -0.166, 18.733, -0.59, 1, 18.844, -0.629, 18.956, -1, 19.067, -1, 1, 19.311, -1, 19.556, 1, 19.8, 1, 1, 20.133, 1, 20.467, 0, 20.8, 0, 1, 21.522, 0, 22.244, 0, 22.967, 0, 1, 23.1, 0, 23.233, -0.09, 23.367, -0.09, 1, 23.611, -0.09, 23.856, -0.09, 24.1, -0.09, 1, 24.267, -0.09, 24.433, -0.5, 24.6, -0.5, 1, 24.689, -0.5, 24.778, -0.5, 24.867, -0.5, 1, 24.944, -0.5, 25.022, -0.5, 25.1, -0.5, 0, 25.133, -0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.178, 1, 1.356, 0.34, 1.533, 0.34, 1, 1.8, 0.34, 2.067, 1, 2.333, 1, 1, 2.856, 1, 3.378, 1, 3.9, 1, 1, 4.133, 1, 4.367, 1, 4.6, 1, 1, 4.744, 1, 4.889, 1, 5.033, 1, 1, 5.311, 1, 5.589, 1, 5.867, 1, 1, 5.956, 1, 6.044, 1, 6.133, 1, 1, 7.233, 1, 8.333, 1, 9.433, 1, 1, 11.322, 1, 13.211, 1, 15.1, 1, 1, 18.267, 1, 21.433, 1, 24.6, 1, 1, 24.689, 1, 24.778, 1, 24.867, 1, 1, 24.944, 1, 25.022, 1, 25.1, 1, 0, 25.133, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0.24, 1.533, 0.24, 1, 1.8, 0.24, 2.067, 0, 2.333, 0, 1, 2.567, 0, 2.8, 0, 3.033, 0, 1, 3.111, 0, 3.189, -1, 3.267, -1, 1, 3.344, -1, 3.422, -1, 3.5, -1, 1, 3.633, -1, 3.767, 0, 3.9, 0, 1, 4.133, 0, 4.367, 0, 4.6, 0, 1, 4.744, 0, 4.889, 0, 5.033, 0, 1, 5.311, 0, 5.589, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.556, 0, 7.778, -1, 8, -1, 1, 8.2, -1, 8.4, -1, 8.6, -1, 1, 8.878, -1, 9.156, 0, 9.433, 0, 1, 10.533, 0, 11.633, 0, 12.733, 0, 1, 12.978, 0, 13.222, 0.14, 13.467, 0.14, 1, 13.733, 0.14, 14, 0.14, 14.267, 0.14, 1, 14.3, 0.14, 14.333, -1, 14.367, -1, 1, 14.411, -1, 14.456, 0.14, 14.5, 0.14, 1, 14.544, 0.14, 14.589, -1, 14.633, -1, 1, 14.667, -1, 14.7, 0.14, 14.733, 0.14, 1, 14.856, 0.14, 14.978, 0.14, 15.1, 0.14, 1, 15.467, 0.14, 15.833, -1, 16.2, -1, 1, 17.4, -1, 18.6, -1, 19.8, -1, 1, 20.133, -1, 20.467, -0.157, 20.8, 0, 1, 21.144, 0.162, 21.489, 0.14, 21.833, 0.14, 1, 21.867, 0.14, 21.9, -1, 21.933, -1, 1, 21.978, -1, 22.022, 0.14, 22.067, 0.14, 1, 22.111, 0.14, 22.156, -1, 22.2, -1, 1, 22.233, -1, 22.267, 0.14, 22.3, 0.14, 1, 22.433, 0.14, 22.567, 0.14, 22.7, 0.14, 1, 22.789, 0.14, 22.878, 0.14, 22.967, 0.14, 1, 23.1, 0.14, 23.233, 0, 23.367, 0, 1, 23.611, 0, 23.856, 0, 24.1, 0, 1, 24.267, 0, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0.24, 1.533, 0.24, 1, 1.8, 0.24, 2.067, 0, 2.333, 0, 1, 2.567, 0, 2.8, 0, 3.033, 0, 1, 3.111, 0, 3.189, -1, 3.267, -1, 1, 3.344, -1, 3.422, -1, 3.5, -1, 1, 3.633, -1, 3.767, 0, 3.9, 0, 1, 4.133, 0, 4.367, 0, 4.6, 0, 1, 4.744, 0, 4.889, 0, 5.033, 0, 1, 5.311, 0, 5.589, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.556, 0, 7.778, -1, 8, -1, 1, 8.2, -1, 8.4, -1, 8.6, -1, 1, 8.878, -1, 9.156, 0, 9.433, 0, 1, 10.533, 0, 11.633, 0, 12.733, 0, 1, 12.978, 0, 13.222, 0.14, 13.467, 0.14, 1, 13.733, 0.14, 14, 0.14, 14.267, 0.14, 1, 14.3, 0.14, 14.333, -1, 14.367, -1, 1, 14.411, -1, 14.456, 0.14, 14.5, 0.14, 1, 14.544, 0.14, 14.589, -1, 14.633, -1, 1, 14.667, -1, 14.7, 0.14, 14.733, 0.14, 1, 14.856, 0.14, 14.978, 0.14, 15.1, 0.14, 1, 15.467, 0.14, 15.833, -1, 16.2, -1, 1, 17.4, -1, 18.6, -1, 19.8, -1, 1, 20.133, -1, 20.467, -0.157, 20.8, 0, 1, 21.144, 0.162, 21.489, 0.14, 21.833, 0.14, 1, 21.867, 0.14, 21.9, -1, 21.933, -1, 1, 21.978, -1, 22.022, 0.14, 22.067, 0.14, 1, 22.111, 0.14, 22.156, -1, 22.2, -1, 1, 22.233, -1, 22.267, 0.14, 22.3, 0.14, 1, 22.433, 0.14, 22.567, 0.14, 22.7, 0.14, 1, 22.789, 0.14, 22.878, 0.14, 22.967, 0.14, 1, 23.1, 0.14, 23.233, 0, 23.367, 0, 1, 23.611, 0, 23.856, 0, 24.1, 0, 1, 24.267, 0, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.967, 0, 2.933, 0, 3.9, 0, 1, 4.133, 0, 4.367, -0.33, 4.6, -0.33, 1, 4.744, -0.33, 4.889, -0.33, 5.033, -0.33, 1, 5.311, -0.33, 5.589, -0.299, 5.867, -0.299, 1, 5.956, -0.299, 6.044, -0.299, 6.133, -0.299, 1, 7.233, -0.299, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, 1, 16.2, 1, 1, 18.233, 1, 20.267, 1, 22.3, 1, 1, 22.522, 1, 22.744, 1, 22.967, 1, 1, 23.1, 1, 23.233, -1, 23.367, -1, 1, 23.611, -1, 23.856, -1, 24.1, -1, 1, 24.267, -1, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.967, 0, 2.933, 0, 3.9, 0, 1, 4.133, 0, 4.367, -0.33, 4.6, -0.33, 1, 4.744, -0.33, 4.889, -0.33, 5.033, -0.33, 1, 5.311, -0.33, 5.589, -0.299, 5.867, -0.299, 1, 5.956, -0.299, 6.044, -0.299, 6.133, -0.299, 1, 7.233, -0.299, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, 1, 16.2, 1, 1, 18.233, 1, 20.267, 1, 22.3, 1, 1, 22.522, 1, 22.744, 1, 22.967, 1, 1, 23.1, 1, 23.233, -1, 23.367, -1, 1, 23.611, -1, 23.856, -1, 24.1, -1, 1, 24.267, -1, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.967, 0, 2.933, 0, 3.9, 0, 1, 4.133, 0, 4.367, -0.6, 4.6, -0.6, 1, 4.744, -0.6, 4.889, -0.6, 5.033, -0.6, 1, 5.311, -0.6, 5.589, -0.421, 5.867, -0.421, 1, 5.956, -0.421, 6.044, -0.421, 6.133, -0.421, 1, 6.533, -0.421, 6.933, 0, 7.333, 0, 1, 7.756, 0, 8.178, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, -0.6, 16.2, -0.6, 1, 18.456, -0.6, 20.711, -0.6, 22.967, -0.6, 1, 23.1, -0.6, 23.233, -1.5, 23.367, -1.5, 1, 23.611, -1.5, 23.856, -1.5, 24.1, -1.5, 1, 24.267, -1.5, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.967, 0, 2.933, 0, 3.9, 0, 1, 4.133, 0, 4.367, -0.6, 4.6, -0.6, 1, 4.744, -0.6, 4.889, -0.6, 5.033, -0.6, 1, 5.311, -0.6, 5.589, -0.421, 5.867, -0.421, 1, 5.956, -0.421, 6.044, -0.421, 6.133, -0.421, 1, 6.533, -0.421, 6.933, 0, 7.333, 0, 1, 7.756, 0, 8.178, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, -0.6, 16.2, -0.6, 1, 18.456, -0.6, 20.711, -0.6, 22.967, -0.6, 1, 23.1, -0.6, 23.233, -1.5, 23.367, -1.5, 1, 23.611, -1.5, 23.856, -1.5, 24.1, -1.5, 1, 24.267, -1.5, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.178, 1, 1.356, -1, 1.533, -1, 1, 1.8, -1, 2.067, 1, 2.333, 1, 1, 2.722, 1, 3.111, 1, 3.5, 1, 1, 3.867, 1, 4.233, 1, 4.6, 1, 1, 4.744, 1, 4.889, 1, 5.033, 1, 1, 5.311, 1, 5.589, 1, 5.867, 1, 1, 5.956, 1, 6.044, 1, 6.133, 1, 1, 6.533, 1, 6.933, 1, 7.333, 1, 1, 7.556, 1, 7.778, -0.83, 8, -0.83, 1, 8.2, -0.83, 8.4, 1, 8.6, 1, 1, 8.878, 1, 9.156, 1, 9.433, 1, 1, 9.844, 1, 10.256, 1, 10.667, 1, 1, 10.989, 1, 11.311, 1, 11.633, 1, 1, 12.789, 1, 13.944, 1, 15.1, 1, 1, 15.467, 1, 15.833, 0.542, 16.2, 0, 1, 16.711, -0.756, 17.222, -1, 17.733, -1, 1, 18.067, -1, 18.4, -1, 18.733, -1, 1, 18.844, -1, 18.956, 0, 19.067, 0, 1, 19.311, 0, 19.556, -1, 19.8, -1, 1, 20.633, -1, 21.467, -1, 22.3, -1, 1, 22.522, -1, 22.744, -1, 22.967, -1, 1, 23.1, -1, 23.233, 1, 23.367, 1, 1, 23.611, 1, 23.856, 1, 24.1, 1, 1, 24.267, 1, 24.433, 1, 24.6, 1, 1, 24.689, 1, 24.778, 1, 24.867, 1, 1, 24.944, 1, 25.022, 1, 25.1, 1, 0, 25.133, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 1, 1.533, 1, 1, 1.8, 1, 2.067, 0, 2.333, 0, 1, 2.722, 0, 3.111, 0, 3.5, 0, 1, 3.867, 0, 4.233, 0, 4.6, 0, 1, 4.744, 0, 4.889, 0, 5.033, 0, 1, 5.311, 0, 5.589, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.556, 0, 7.778, 0.56, 8, 0.56, 1, 8.2, 0.56, 8.4, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 0.5, 10.667, 0.5, 1, 10.989, 0.5, 11.311, 0.5, 11.633, 0.5, 1, 12.789, 0.5, 13.944, 0.5, 15.1, 0.5, 1, 15.467, 0.5, 15.833, 1, 16.2, 1, 1, 16.711, 1, 17.222, 0, 17.733, 0, 1, 18.067, 0, 18.4, 0, 18.733, 0, 1, 18.844, 0, 18.956, 1, 19.067, 1, 1, 19.311, 1, 19.556, 0.67, 19.8, 0.67, 1, 20.633, 0.67, 21.467, 0.67, 22.3, 0.67, 1, 22.522, 0.67, 22.744, 0.67, 22.967, 0.67, 1, 23.1, 0.67, 23.233, 0, 23.367, 0, 1, 23.611, 0, 23.856, 0, 24.1, 0, 1, 24.267, 0, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek_01", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 2.978, 0, 4.422, 0.84, 5.867, 0.84, 1, 5.956, 0.84, 6.044, 0.84, 6.133, 0.84, 1, 6.533, 0.84, 6.933, 1, 7.333, 1, 1, 7.756, 1, 8.178, 1, 8.6, 1, 1, 8.878, 1, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 0, 10.667, 0, 1, 10.989, 0, 11.311, 0, 11.633, 0, 1, 12.789, 0, 13.944, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek_02", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 2.978, 0, 4.422, 0.84, 5.867, 0.84, 1, 5.956, 0.84, 6.044, 0.84, 6.133, 0.84, 1, 6.533, 0.84, 6.933, 1, 7.333, 1, 1, 7.756, 1, 8.178, 1, 8.6, 1, 1, 8.878, 1, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 1, 10.667, 1, 1, 10.989, 1, 11.311, 1, 11.633, 1, 1, 12.156, 1, 12.678, 1, 13.2, 1, 1, 13.367, 1, 13.533, 0, 13.7, 0, 1, 14.167, 0, 14.633, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek_03", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.756, 0, 8.178, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 0, 10.667, 0, 1, 10.989, 0, 11.311, 0, 11.633, 0, 1, 12.789, 0, 13.944, 0, 15.1, 0, 1, 17.722, 0, 20.344, 0, 22.967, 0, 1, 23.1, 0, 23.233, 1, 23.367, 1, 1, 23.611, 1, 23.856, 1, 24.1, 1, 1, 24.267, 1, 24.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek_04", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.756, 0, 8.178, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 0, 10.667, 0, 1, 10.989, 0, 11.311, 0, 11.633, 0, 1, 12.789, 0, 13.944, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.733, 0, 2.467, 0, 3.2, 0, 1, 4.089, 0, 4.978, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.756, 0, 8.178, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, -3, 16.2, -3, 1, 16.433, -3, 16.667, 4, 16.9, 4, 1, 17.178, 4, 17.456, -4, 17.733, -4, 1, 18.067, -4, 18.4, 1.85, 18.733, 6, 1, 18.844, 7.383, 18.956, 7, 19.067, 7, 1, 19.311, 7, 19.556, 5.974, 19.8, 4, 1, 20.133, 1.308, 20.467, 0, 20.8, 0, 1, 22.067, 0, 23.333, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, -1, 1, -1, 1, 1.178, -1, 1.356, 10, 1.533, 10, 1, 1.744, 10, 1.956, 0, 2.167, 0, 1, 2.333, 0, 2.5, 0, 2.667, 0, 1, 2.8, 0, 2.933, -4, 3.067, -4, 1, 3.833, -4, 4.6, 0, 5.367, 0, 1, 5.533, 0, 5.7, -0.966, 5.867, -0.966, 1, 5.956, -0.966, 6.044, -0.966, 6.133, -0.966, 1, 6.756, -0.966, 7.378, -10, 8, -10, 1, 8.2, -10, 8.4, -10, 8.6, -10, 1, 8.878, -10, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, -10, 16.2, -10, 1, 17.733, -10, 19.267, 0, 20.8, 0, 1, 22.067, 0, 23.333, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, -3, 1, -3, 1, 1.444, -3, 1.889, 0, 2.333, 0, 1, 2.478, 0, 2.622, 0, 2.767, 0, 1, 2.989, 0, 3.211, -4, 3.433, -4, 1, 3.722, -4, 4.011, 0, 4.3, 0, 1, 4.489, 0, 4.678, -5, 4.867, -5, 1, 5.033, -5, 5.2, 0, 5.367, 0, 1, 5.533, 0, 5.7, -0.837, 5.867, -0.837, 1, 5.956, -0.837, 6.044, -0.837, 6.133, -0.837, 1, 6.533, -0.837, 6.933, -5, 7.333, -5, 1, 7.556, -5, 7.778, 2, 8, 2, 1, 8.2, 2, 8.4, 1.544, 8.6, 1, 1, 8.878, 0.245, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, 10, 10.667, 10, 1, 10.989, 10, 11.311, -10, 11.633, -10, 1, 12, -10, 12.367, 10, 12.733, 10, 1, 13.522, 10, 14.311, 10, 15.1, 10, 1, 15.467, 10, 15.833, -2, 16.2, -2, 1, 17.044, -2, 17.889, -2, 18.733, -2, 1, 18.844, -2, 18.956, 0, 19.067, 0, 1, 19.311, 0, 19.556, -10, 19.8, -10, 1, 20.133, -10, 20.467, 0, 20.8, 0, 1, 22.067, 0, 23.333, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, -0.103, 1.533, -0.15, 1, 2.011, -0.278, 2.489, -0.31, 2.967, -0.31, 1, 3.2, -0.31, 3.433, 0, 3.667, 0, 1, 4.4, 0, 5.133, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, 0, 7.333, 0, 1, 7.5, 0, 7.667, 0.966, 7.833, 0.98, 1, 8.089, 1.002, 8.344, 1, 8.6, 1, 1, 8.878, 1, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamTie", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 9.122, 0, 12.111, 0, 15.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, -1, 1.533, -1, 1, 1.8, -1, 2.067, 0, 2.333, 0, 1, 2.567, 0, 2.8, 0, 3.033, 0, 1, 3.111, 0, 3.189, -0.37, 3.267, -0.37, 1, 3.344, -0.37, 3.422, -0.144, 3.5, 0, 1, 3.767, 0.493, 4.033, 0.66, 4.3, 0.66, 1, 4.544, 0.66, 4.789, -0.54, 5.033, -0.54, 1, 5.178, -0.54, 5.322, 0.35, 5.467, 0.35, 1, 5.6, 0.35, 5.733, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.533, 0, 6.933, -1, 7.333, -1, 1, 7.556, -1, 7.778, 1, 8, 1, 1, 8.478, 1, 8.956, 0, 9.433, 0, 1, 9.656, 0, 9.878, 1, 10.1, 1, 1, 10.289, 1, 10.478, -1, 10.667, -1, 1, 10.822, -1, 10.978, 1, 11.133, 1, 1, 11.3, 1, 11.467, -1, 11.633, -1, 1, 11.822, -1, 12.011, 1, 12.2, 1, 1, 12.378, 1, 12.556, -1, 12.733, -1, 1, 12.889, -1, 13.044, 1, 13.2, 1, 1, 13.289, 1, 13.378, 0, 13.467, 0, 1, 13.544, 0, 13.622, 1, 13.7, 1, 1, 13.889, 1, 14.078, 1, 14.267, 1, 1, 14.3, 1, 14.333, 0.8, 14.367, 0.8, 1, 14.411, 0.8, 14.456, 1, 14.5, 1, 1, 14.544, 1, 14.589, 0.72, 14.633, 0.72, 1, 14.667, 0.72, 14.7, 1, 14.733, 1, 1, 15.033, 1, 15.333, 0, 15.633, 0, 1, 15.822, 0, 16.011, 1, 16.2, 1, 1, 16.433, 1, 16.667, -1, 16.9, -1, 1, 17.178, -1, 17.456, 1, 17.733, 1, 1, 18.067, 1, 18.4, 0.388, 18.733, 0, 1, 19.422, -0.802, 20.111, -1, 20.8, -1, 1, 22.067, -1, 23.333, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.967, 0, 2.933, 0, 3.9, 0, 1, 4.133, 0, 4.367, 0, 4.6, 0, 1, 5.022, 0, 5.444, -0.452, 5.867, -0.452, 1, 5.956, -0.452, 6.044, -0.452, 6.133, -0.452, 1, 6.533, -0.452, 6.933, -1, 7.333, -1, 1, 7.756, -1, 8.178, -1, 8.6, -1, 1, 8.878, -1, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 15.467, 0, 15.833, -1, 16.2, -1, 1, 17.4, -1, 18.6, -1, 19.8, -1, 1, 20.133, -1, 20.467, 0, 20.8, 0, 1, 22.067, 0, 23.333, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamRibonL", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamRibonR", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 2.622, 0, 4.244, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 7.233, 0, 8.333, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.333, 0, 0.667, -0.64, 1, -0.64, 1, 1.178, -0.64, 1.356, 0, 1.533, 0, 1, 2.322, 0, 3.111, 0, 3.9, 0, 1, 4.133, 0, 4.367, -0.53, 4.6, -0.53, 1, 5.022, -0.53, 5.444, -0.138, 5.867, -0.138, 1, 5.956, -0.138, 6.044, -0.138, 6.133, -0.138, 1, 6.289, -0.138, 6.444, 0.237, 6.6, 0.5, 1, 6.844, 0.914, 7.089, 1, 7.333, 1, 1, 7.756, 1, 8.178, 1, 8.6, 1, 1, 8.878, 1, 9.156, 0.44, 9.433, 0.44, 1, 11.322, 0.44, 13.211, 0.44, 15.1, 0.44, 1, 15.467, 0.44, 15.833, -1, 16.2, -1, 1, 17.4, -1, 18.6, -1, 19.8, -1, 1, 20.133, -1, 20.467, 0, 20.8, 0, 1, 22.067, 0, 23.333, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 2.456, 0, 3.378, 0, 4.3, 0, 1, 4.689, 0, 5.078, 0, 5.467, 0, 1, 5.6, 0, 5.733, 0, 5.867, 0, 1, 5.956, 0, 6.044, 0, 6.133, 0, 1, 6.4, 0, 6.667, 0, 6.933, 0, 1, 7.067, 0, 7.2, -0.027, 7.333, 0.091, 1, 7.5, 0.237, 7.667, 1, 7.833, 1, 1, 8.089, 1, 8.344, 1, 8.6, 1, 1, 8.878, 1, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.178, 0, 1.356, 0, 1.533, 0, 1, 2.456, 0, 3.378, 0, 4.3, 0, 1, 4.722, 0, 5.144, -1, 5.567, -1, 1, 5.667, -1, 5.767, -1, 5.867, -1, 1, 5.956, -1, 6.044, -1, 6.133, -1, 1, 6.533, -1, 6.933, -1, 7.333, -1, 1, 7.756, -1, 8.178, -1, 8.6, -1, 1, 8.878, -1, 9.156, 0, 9.433, 0, 1, 11.322, 0, 13.211, 0, 15.1, 0, 1, 18.267, 0, 21.433, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 2.622, 0.5, 4.244, 0.5, 5.867, 0.5, 1, 5.956, 0.5, 6.044, 0.5, 6.133, 0.5, 1, 6.533, 0.5, 6.933, 0.5, 7.333, 0.5, 1, 7.756, 0.5, 8.178, 0.5, 8.6, 0.5, 1, 8.878, 0.5, 9.156, 0.5, 9.433, 0.5, 1, 11.322, 0.5, 13.211, 0.5, 15.1, 0.5, 1, 18.267, 0.5, 21.433, 0.5, 24.6, 0.5, 1, 24.689, 0.5, 24.778, 0.5, 24.867, 0.5, 1, 24.944, 0.5, 25.022, 0.5, 25.1, 0.5, 0, 25.133, 0.5]}, {"Target": "Parameter", "Id": "ParamLegL", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 1, 3.078, 0, 3.356, -1, 3.633, -1, 1, 3.856, -1, 4.078, -1, 4.3, -1, 1, 4.822, -1, 5.344, -0.58, 5.867, -0.58, 1, 5.956, -0.58, 6.044, -0.58, 6.133, -0.58, 1, 6.7, -0.58, 7.267, 0, 7.833, 0, 1, 8.089, 0, 8.344, 0, 8.6, 0, 1, 8.878, 0, 9.156, 0, 9.433, 0, 1, 9.844, 0, 10.256, -0.4, 10.667, -0.4, 1, 10.989, -0.4, 11.311, 0.4, 11.633, 0.4, 1, 12, 0.4, 12.367, -0.4, 12.733, -0.4, 1, 13.522, -0.4, 14.311, -0.4, 15.1, -0.4, 1, 15.467, -0.4, 15.833, 0, 16.2, 0, 1, 19, 0, 21.8, 0, 24.6, 0, 1, 24.689, 0, 24.778, 0, 24.867, 0, 1, 24.944, 0, 25.022, 0, 25.1, 0, 0, 25.133, 0]}]}