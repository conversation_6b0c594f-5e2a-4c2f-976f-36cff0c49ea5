import React from 'react';

export const SimpleInput = ({ 
  label,
  defaultValue = '',
  value,
  onChange,
  placeholder = '',
  type = 'text',
  className = '', 
  ...props 
}) => {
  const containerStyles = {
    display: 'flex',
    flexDirection: 'column',
    gap: '0.25rem',
    width: '100%',
  };
  
  const labelStyles = {
    fontWeight: 'medium',
    fontSize: '0.875rem',
  };
  
  const inputStyles = {
    padding: '0.5rem 0.75rem',
    borderRadius: '0.375rem',
    border: '1px solid #cbd5e0',
    outline: 'none',
    transition: 'border-color 0.2s',
    ':focus': {
      borderColor: '#3182ce',
      boxShadow: '0 0 0 1px #3182ce',
    },
  };
  
  return (
    <div style={containerStyles} className={className}>
      {label && <label style={labelStyles}>{label}</label>}
      <input
        type={type}
        defaultValue={defaultValue}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        style={inputStyles}
        {...props}
      />
    </div>
  );
};

export default SimpleInput;
