{"Version": 3, "Meta": {"Duration": 2.53, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 139, "TotalPointCount": 379, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 3, 1, 0.244, 3, 0.489, 3, 0.733, 3, 1, 0.922, 3, 1.111, 3, 1.3, 3, 1, 1.478, 3, 1.656, -2, 1.833, -2, 0, 2.533, -2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 15, 1, 0.244, 15, 0.489, 15, 0.733, 15, 1, 0.922, 15, 1.111, 15, 1.3, 15, 1, 1.478, 15, 1.656, -16, 1.833, -16, 1, 1.9, -16, 1.967, -12, 2.033, -12, 0, 2.533, -12]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 11, 1, 0.244, 11, 0.489, 11, 0.733, 11, 1, 0.922, 11, 1.111, 11, 1.3, 11, 1, 1.378, 11, 1.456, 12, 1.533, 12, 1, 1.633, 12, 1.733, -30, 1.833, -30, 1, 1.889, -30, 1.944, -25, 2, -25, 0, 2.533, -25]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1.5, 1, 0.244, 1.5, 0.489, 1.5, 0.733, 1.5, 1, 0.778, 1.5, 0.822, 0, 0.867, 0, 1, 0.889, 0, 0.911, 0, 0.933, 0, 1, 0.989, 0, 1.044, 1.5, 1.1, 1.5, 1, 1.167, 1.5, 1.233, 1.5, 1.3, 1.5, 1, 1.433, 1.5, 1.567, 1.5, 1.7, 1.5, 1, 1.744, 1.5, 1.789, 0, 1.833, 0, 1, 1.856, 0, 1.878, 0, 1.9, 0, 1, 1.956, 0, 2.011, 1.5, 2.067, 1.5, 0, 2.533, 1.5]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.856, 0, 0.978, 0, 1.1, 0, 1, 1.167, 0, 1.233, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1.5, 1, 0.244, 1.5, 0.489, 1.5, 0.733, 1.5, 1, 0.778, 1.5, 0.822, 0, 0.867, 0, 1, 0.889, 0, 0.911, 0, 0.933, 0, 1, 0.989, 0, 1.044, 1.5, 1.1, 1.5, 1, 1.167, 1.5, 1.233, 1.5, 1.3, 1.5, 1, 1.433, 1.5, 1.567, 1.5, 1.7, 1.5, 1, 1.744, 1.5, 1.789, 0, 1.833, 0, 1, 1.856, 0, 1.878, 0, 1.9, 0, 1, 1.956, 0, 2.011, 1.5, 2.067, 1.5, 0, 2.533, 1.5]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.856, 0, 0.978, 0, 1.1, 0, 1, 1.167, 0, 1.233, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.25, 1, 0.244, -0.25, 0.489, -0.25, 0.733, -0.25, 1, 0.922, -0.25, 1.111, -0.25, 1.3, -0.25, 1, 1.478, -0.25, 1.656, 0.19, 1.833, 0.19, 0, 2.533, 0.19]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.33, 1, 0.244, -0.33, 0.489, -0.33, 0.733, -0.33, 1, 0.922, -0.33, 1.111, -0.33, 1.3, -0.33, 1, 1.478, -0.33, 1.656, 0.33, 1.833, 0.33, 0, 2.533, 0.33]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.53, 1, 0.244, 0.53, 0.489, 0.53, 0.733, 0.53, 1, 0.922, 0.53, 1.111, 0.53, 1.3, 0.53, 0, 2.533, 0.53]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.42, 1, 0.244, 0.42, 0.489, 0.42, 0.733, 0.42, 1, 0.922, 0.42, 1.111, 0.42, 1.3, 0.42, 0, 2.533, 0.42]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.28, 1, 0.244, 0.28, 0.489, 0.28, 0.733, 0.28, 1, 0.922, 0.28, 1.111, 0.28, 1.3, 0.28, 0, 2.533, 0.28]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.22, 1, 0.244, 0.22, 0.489, 0.22, 0.733, 0.22, 1, 0.922, 0.22, 1.111, 0.22, 1.3, 0.22, 0, 2.533, 0.22]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.244, -1, 0.489, -1, 0.733, -1, 1, 0.922, -1, 1.111, -1, 1.3, -1, 0, 2.533, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 1, 1, 0.244, 1, 0.489, 1, 0.733, 1, 1, 0.922, 1, 1.111, 1, 1.3, 1, 0, 2.533, 1]}, {"Target": "Parameter", "Id": "ParamRage", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 1, 1.478, 0, 1.656, -2, 1.833, -2, 0, 2.533, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 1, 1.378, 0, 1.456, 5, 1.533, 5, 1, 1.633, 5, 1.733, -3, 1.833, -3, 0, 2.533, -3]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_L", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_R", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_L", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_R", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.922, 0, 1.111, 0, 1.3, 0, 0, 2.533, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_FACE_001_c", "Segments": [0, 0, 0, 2.53, 0]}]}