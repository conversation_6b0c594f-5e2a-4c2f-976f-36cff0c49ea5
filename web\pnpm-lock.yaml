lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@headlessui/react':
        specifier: ^2.0.4
        version: 2.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@heroicons/react':
        specifier: ^2.1.3
        version: 2.1.5(react@18.3.1)
      '@nextui-org/button':
        specifier: 2.0.34
        version: 2.0.34(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/card':
        specifier: ^2.0.33
        version: 2.0.33(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/code':
        specifier: 2.0.29
        version: 2.0.29(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/input':
        specifier: 2.2.2
        version: 2.2.2(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/kbd':
        specifier: 2.0.30
        version: 2.0.30(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/link':
        specifier: 2.0.32
        version: 2.0.32(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/listbox':
        specifier: 2.1.21
        version: 2.1.21(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/modal':
        specifier: ^2.0.36
        version: 2.0.39(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/navbar':
        specifier: 2.0.33
        version: 2.0.33(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(@types/react@18.3.5)(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/radio':
        specifier: ^2.1.2
        version: 2.1.4(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react':
        specifier: ^2.6.11
        version: 2.6.11(@types/react@18.3.5)(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(tailwindcss@3.4.10)
      '@nextui-org/snippet':
        specifier: 2.0.38
        version: 2.0.38(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/switch':
        specifier: 2.0.31
        version: 2.0.31(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system':
        specifier: 2.2.1
        version: 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/table':
        specifier: ^2.0.36
        version: 2.0.39(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme':
        specifier: 2.2.5
        version: 2.2.5(tailwindcss@3.4.10)
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      eruda:
        specifier: ^3.3.0
        version: 3.3.0
      framer-motion:
        specifier: ^12.11.0
        version: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      js-audio-recorder:
        specifier: ^1.0.7
        version: 1.0.7
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      next:
        specifier: 14.2.3
        version: 14.2.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      or:
        specifier: ^0.2.0
        version: 0.2.0
      react:
        specifier: ^18
        version: 18.3.1
      react-dom:
        specifier: ^18
        version: 18.3.1(react@18.3.1)
      react-markdown:
        specifier: ^9.0.1
        version: 9.0.1(@types/react@18.3.5)(react@18.3.1)
      react-use:
        specifier: ^17.5.1
        version: 17.5.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-use-websocket:
        specifier: ^4.8.1
        version: 4.8.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      websocket:
        specifier: ^1.0.35
        version: 1.0.35
      whatwg-fetch:
        specifier: ^3.6.20
        version: 3.6.20
      zustand:
        specifier: ^4.5.2
        version: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    devDependencies:
      '@types/node':
        specifier: ^20
        version: 20.16.2
      '@types/react':
        specifier: ^18
        version: 18.3.5
      '@types/react-dom':
        specifier: ^18
        version: 18.3.0
      eslint:
        specifier: ^8
        version: 8.57.0
      eslint-config-next:
        specifier: 14.2.3
        version: 14.2.3(eslint@8.57.0)(typescript@5.5.4)
      postcss:
        specifier: ^8
        version: 8.4.41
      tailwindcss:
        specifier: ^3.4.1
        version: 3.4.10
      typescript:
        specifier: ^5
        version: 5.5.4

packages:

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  '@babel/runtime@7.25.6':
    resolution: {integrity: sha512-VBj9MYyDb9tuLq7yzqjgzt6Q+IBQLrGZfdjOekyEirZPHxXWoTSGUTMrpsfi58Up73d13NfYLv8HT9vmznjzhQ==}
    engines: {node: '>=6.9.0'}

  '@eslint-community/eslint-utils@4.4.0':
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.11.0':
    resolution: {integrity: sha512-G/M/tIiMrTAxEWRfLfQJMmGNX28IxBg4PBz8XqQhqUHLFI6TL2htpIB1iQCj144V5ee/JaKyT9/WZ0MGZWfA7A==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.57.0':
    resolution: {integrity: sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@floating-ui/core@1.6.7':
    resolution: {integrity: sha512-yDzVT/Lm101nQ5TCVeK65LtdN7Tj4Qpr9RTXJ2vPFLqtLxwOrpoxAHAJI8J3yYWUc40J0BDBheaitK5SJmno2g==}

  '@floating-ui/dom@1.6.10':
    resolution: {integrity: sha512-fskgCFv8J8OamCmyun8MfjB1Olfn+uZKjOKZ0vhYF3gRmEUXcGOjxWL8bBr7i4kIuPZ2KD2S3EUIOxnjC8kl2A==}

  '@floating-ui/react-dom@2.1.1':
    resolution: {integrity: sha512-4h84MJt3CHrtG18mGsXuLCHMrug49d7DFkU0RMIyshRveBeyV2hmV/pDaF2Uxtu8kgq5r46llp5E5FQiR0K2Yg==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/react@0.26.23':
    resolution: {integrity: sha512-9u3i62fV0CFF3nIegiWiRDwOs7OW/KhSUJDNx2MkQM3LbE5zQOY01sL3nelcVBXvX7Ovvo3A49I8ql+20Wg/Hw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.7':
    resolution: {integrity: sha512-X8R8Oj771YRl/w+c1HqAC1szL8zWQRwFvgDwT129k9ACdBoud/+/rX9V0qiMl6LWUdP9voC2nDVZYPMQQsb6eA==}

  '@formatjs/ecma402-abstract@2.0.0':
    resolution: {integrity: sha512-rRqXOqdFmk7RYvj4khklyqzcfQl9vEL/usogncBHRZfZBDOwMGuSRNFl02fu5KGHXdbinju+YXyuR+Nk8xlr/g==}

  '@formatjs/fast-memoize@2.2.0':
    resolution: {integrity: sha512-hnk/nY8FyrL5YxwP9e4r9dqeM6cAbo8PeU9UjyXojZMNvVad2Z06FAVHyR3Ecw6fza+0GH7vdJgiKIVXTMbSBA==}

  '@formatjs/icu-messageformat-parser@2.7.8':
    resolution: {integrity: sha512-nBZJYmhpcSX0WeJ5SDYUkZ42AgR3xiyhNCsQweFx3cz/ULJjym8bHAzWKvG5e2+1XO98dBYC0fWeeAECAVSwLA==}

  '@formatjs/icu-skeleton-parser@1.8.2':
    resolution: {integrity: sha512-k4ERKgw7aKGWJZgTarIcNEmvyTVD9FYh0mTrrBMHZ1b8hUu6iOJ4SzsZlo3UNAvHYa+PnvntIwRPt1/vy4nA9Q==}

  '@formatjs/intl-localematcher@0.5.4':
    resolution: {integrity: sha512-zTwEpWOzZ2CiKcB93BLngUX59hQkuZjT2+SAQEscSm52peDW/getsawMcWF1rGRpMCX6D7nSJA3CzJ8gn13N/g==}

  '@headlessui/react@2.1.3':
    resolution: {integrity: sha512-Nt+NlnQbVvMHVZ/QsST6DNyfG8VWqjOYY3eZpp0PrRKpmZw+pzhwQ1F6wtNaW4jnudeC2a5MJC70vbGVcETNIg==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^18
      react-dom: ^18

  '@heroicons/react@2.1.5':
    resolution: {integrity: sha512-FuzFN+BsHa+7OxbvAERtgBTNeZpUjgM/MIizfVkSCL2/edriN0Hx/DWRCR//aPYwO5QX/YlgLGXk+E3PcfZwjA==}
    peerDependencies:
      react: '>= 16'

  '@humanwhocodes/config-array@0.11.14':
    resolution: {integrity: sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==}
    deprecated: Use @eslint/object-schema instead

  '@internationalized/date@3.5.5':
    resolution: {integrity: sha512-H+CfYvOZ0LTJeeLOqm19E3uj/4YjrmOFtBufDHPfvtI80hFAMqtrp7oCACpe4Cil5l8S0Qu/9dYfZc/5lY8WQQ==}

  '@internationalized/date@3.6.0':
    resolution: {integrity: sha512-+z6ti+CcJnRlLHok/emGEsWQhe7kfSmEW+/6qCzvKY67YPh7YOBfvc7+/+NXq+zJlbArg30tYpqLjNgcAYv2YQ==}

  '@internationalized/date@3.8.0':
    resolution: {integrity: sha512-J51AJ0fEL68hE4CwGPa6E0PO6JDaVLd8aln48xFCSy7CZkZc96dGEGmLs2OEEbBxcsVZtfrqkXJwI2/MSG8yKw==}

  '@internationalized/message@3.1.4':
    resolution: {integrity: sha512-Dygi9hH1s7V9nha07pggCkvmRfDd3q2lWnMGvrJyrOwYMe1yj4D2T9BoH9I6MGR7xz0biQrtLPsqUkqXzIrBOw==}

  '@internationalized/message@3.1.7':
    resolution: {integrity: sha512-gLQlhEW4iO7DEFPf/U7IrIdA3UyLGS0opeqouaFwlMObLUzwexRjbygONHDVbC9G9oFLXsLyGKYkJwqXw/QADg==}

  '@internationalized/number@3.5.3':
    resolution: {integrity: sha512-rd1wA3ebzlp0Mehj5YTuTI50AQEx80gWFyHcQu+u91/5NgdwBecO8BH6ipPfE+lmQ9d63vpB3H9SHoIUiupllw==}

  '@internationalized/number@3.6.1':
    resolution: {integrity: sha512-UVsb4bCwbL944E0SX50CHFtWEeZ2uB5VozZ5yDXJdq6iPZsZO5p+bjVMZh2GxHf4Bs/7xtDCcPwEa2NU9DaG/g==}

  '@internationalized/string@3.2.3':
    resolution: {integrity: sha512-9kpfLoA8HegiWTeCbR2livhdVeKobCnVv8tlJ6M2jF+4tcMqDo94ezwlnrUANBWPgd8U7OXIHCk2Ov2qhk4KXw==}

  '@internationalized/string@3.2.6':
    resolution: {integrity: sha512-LR2lnM4urJta5/wYJVV7m8qk5DrMZmLRTuFhbQO5b9/sKLHgty6unQy1Li4+Su2DWydmB4aZdS5uxBRXIq2aAw==}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@next/env@14.2.3':
    resolution: {integrity: sha512-W7fd7IbkfmeeY2gXrzJYDx8D2lWKbVoTIj1o1ScPHNzvp30s1AuoEFSdr39bC5sjxJaxTtq3OTCZboNp0lNWHA==}

  '@next/eslint-plugin-next@14.2.3':
    resolution: {integrity: sha512-L3oDricIIjgj1AVnRdRor21gI7mShlSwU/1ZGHmqM3LzHhXXhdkrfeNY5zif25Bi5Dd7fiJHsbhoZCHfXYvlAw==}

  '@next/swc-darwin-arm64@14.2.3':
    resolution: {integrity: sha512-3pEYo/RaGqPP0YzwnlmPN2puaF2WMLM3apt5jLW2fFdXD9+pqcoTzRk+iZsf8ta7+quAe4Q6Ms0nR0SFGFdS1A==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@next/swc-darwin-x64@14.2.3':
    resolution: {integrity: sha512-6adp7waE6P1TYFSXpY366xwsOnEXM+y1kgRpjSRVI2CBDOcbRjsJ67Z6EgKIqWIue52d2q/Mx8g9MszARj8IEA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@next/swc-linux-arm64-gnu@14.2.3':
    resolution: {integrity: sha512-cuzCE/1G0ZSnTAHJPUT1rPgQx1w5tzSX7POXSLaS7w2nIUJUD+e25QoXD/hMfxbsT9rslEXugWypJMILBj/QsA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-arm64-musl@14.2.3':
    resolution: {integrity: sha512-0D4/oMM2Y9Ta3nGuCcQN8jjJjmDPYpHX9OJzqk42NZGJocU2MqhBq5tWkJrUQOQY9N+In9xOdymzapM09GeiZw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-x64-gnu@14.2.3':
    resolution: {integrity: sha512-ENPiNnBNDInBLyUU5ii8PMQh+4XLr4pG51tOp6aJ9xqFQ2iRI6IH0Ds2yJkAzNV1CfyagcyzPfROMViS2wOZ9w==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-linux-x64-musl@14.2.3':
    resolution: {integrity: sha512-BTAbq0LnCbF5MtoM7I/9UeUu/8ZBY0i8SFjUMCbPDOLv+un67e2JgyN4pmgfXBwy/I+RHu8q+k+MCkDN6P9ViQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-win32-arm64-msvc@14.2.3':
    resolution: {integrity: sha512-AEHIw/dhAMLNFJFJIJIyOFDzrzI5bAjI9J26gbO5xhAKHYTZ9Or04BesFPXiAYXDNdrwTP2dQceYA4dL1geu8A==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@next/swc-win32-ia32-msvc@14.2.3':
    resolution: {integrity: sha512-vga40n1q6aYb0CLrM+eEmisfKCR45ixQYXuBXxOOmmoV8sYST9k7E3US32FsY+CkkF7NtzdcebiFT4CHuMSyZw==}
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]

  '@next/swc-win32-x64-msvc@14.2.3':
    resolution: {integrity: sha512-Q1/zm43RWynxrO7lW4ehciQVj+5ePBhOK+/K2P7pLFX3JaJ/IZVC69SHidrmZSOkqz7ECIOhhy7XhAFG4JYyHA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@nextui-org/accordion@2.2.7':
    resolution: {integrity: sha512-jdobOwUxSi617m+LpxHFzg64UhDuOfDJI2CMk3MP+b2WBJ7SNW4hmN2NW5Scx5JiY+kyBGmlxJ4Y++jZpZgQjQ==}
    deprecated: This package has been deprecated. Please use @heroui/accordion instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/alert@2.2.9':
    resolution: {integrity: sha512-SjMZewEqknx/jqmMcyQdbeo6RFg40+A3b1lGjnj/fdkiJozQoTesiOslzDsacqiSgvso2F+8u1emC2tFBAU3hw==}
    deprecated: This package has been deprecated. Please use @heroui/alert instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/aria-utils@2.0.20':
    resolution: {integrity: sha512-3jZ/KsFNDAVN8TnbmNre0igFrUjGxTMiZtdQ5N7vkxcA+TZ+F9HKdSsTKLQC7IdSxSTs2WP5Pd9v31UWQ+rHvw==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/aria-utils@2.0.21':
    resolution: {integrity: sha512-aQXFVm4qNrXrUAHhRtr363BgRDX+zgN3Vm+7bW1qtMbnMGOqTWApCD48FP59bka5JArd3K+85tFEhkdD+UfKbQ==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/aria-utils@2.2.7':
    resolution: {integrity: sha512-QgMZ8fii6BCI/+ZIkgXgkm/gMNQ92pQJn83q90fBT6DF+6j4hsCpJwLNCF5mIJkX/cQ/4bHDsDaj7w1OzkhQNg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/autocomplete@2.3.9':
    resolution: {integrity: sha512-1AizOvL8lERoWjm8WiA0NPJWB3h0gqYlbV/qGZeacac5356hb8cNzWUlxGzr9bNkhn9slIoEUyGMgtYeKq7ptg==}
    deprecated: This package has been deprecated. Please use @heroui/autocomplete instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/avatar@2.2.6':
    resolution: {integrity: sha512-QRNCAMXnSZrFJYKo78lzRPiAPRq5pn1LIHUVvX/mCRiTvbu1FXrMakAvOWz/n1X1mLndnrfQMRNgmtC8YlHIdg==}
    deprecated: This package has been deprecated. Please use @heroui/avatar instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/badge@2.2.5':
    resolution: {integrity: sha512-8pLbuY+RVCzI/00CzNudc86BiuXByPFz2yHh00djKvZAXbT0lfjvswClJxSC2FjUXlod+NtE+eHmlhSMo3gmpw==}
    deprecated: This package has been deprecated. Please use @heroui/badge instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/breadcrumbs@2.2.6':
    resolution: {integrity: sha512-TlAUSiIClmm02tJqOvtwySpKDOENduXCXkKzCbmSaqEFhziHnhyE0eM8IVEprBoK6z1VP+sUrX6C2gZ871KUSw==}
    deprecated: This package has been deprecated. Please use @heroui/breadcrumbs instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/button@2.0.34':
    resolution: {integrity: sha512-VeFpOs7trX6u6FqeGr0XCpuNqPhXTLqsmt4iaygvheZCbzrTKvWHd4QMqSh2CPsNH8UFUBSFJjr3oaf3a0SYWQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/button@2.2.9':
    resolution: {integrity: sha512-RrfjAZHoc6nmaqoLj40M0Qj3tuDdv2BMGCgggyWklOi6lKwtOaADPvxEorDwY3GnN54Xej+9SWtUwE8Oc3SnOg==}
    deprecated: This package has been deprecated. Please use @heroui/button instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/calendar@2.2.9':
    resolution: {integrity: sha512-tx1401HLnwadoDHNkmEIZNeAw9uYW6KsgIRRQnXTNVstBXdMmPWjoMBj8fkQqF55+U58k6a+w3N4tTpgRGOpaQ==}
    deprecated: This package has been deprecated. Please use @heroui/calendar instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/card@2.0.33':
    resolution: {integrity: sha512-iO/ThbUz75YlcFrWO9EssMhOxbc9LN0SSk181+2QnPDbKls9wbkUEfGjq/d9k3h6jb9FaR5N5XwVpT4aUt2Usw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/card@2.2.9':
    resolution: {integrity: sha512-Ltvb5Uy4wwkBJj3QvVQmoB6PwLYUNSoWAFo2xxu7LUHKWcETYI0YbUIuwL2nFU2xfJYeBTGjXGQO1ffBsowrtQ==}
    deprecated: This package has been deprecated. Please use @heroui/card instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/checkbox@2.1.4':
    resolution: {integrity: sha512-74AD4imL064mvs4trQKQj/efwIZYaBt0TmXO6jV+6xGE6S9YjCAy+OBotrgRBG9fURQVQU1qJGnwwsOIdxCXkA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/checkbox@2.3.8':
    resolution: {integrity: sha512-T5+AhzQfbg53qZnPn5rgMcJ7T5rnvSGYTx17wHWtdF9Q4QflZOmLGoxqoTWbTVpM4XzUUPyi7KVSKZScWdBDAA==}
    deprecated: This package has been deprecated. Please use @heroui/checkbox instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.3'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/chip@2.2.6':
    resolution: {integrity: sha512-HrSYagbrD4u4nblsNMIu7WGnDj9A8YnYCt30tasJmNSyydUVHFkxKOc3S8k+VU3BHPxeENxeBT7w0OlYoKbFIQ==}
    deprecated: This package has been deprecated. Please use @heroui/chip instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/code@2.0.29':
    resolution: {integrity: sha512-+aevUjVJxSkJ4Un/O3rBdI1NfHikatzDK6iD6nqWDCDR/I+9a5m+s3N8yuNt/Mt8jGKg0KEklPh3deYfCVCXdg==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/code@2.2.6':
    resolution: {integrity: sha512-8qvAywIKAVh1thy/YHNwqH2xjTcwPiOWwNdKqvJMSk0CNtLHYJmDK8i2vmKZTM3zfB08Q/G94H0Wf+YsyrZdDg==}
    deprecated: This package has been deprecated. Please use @heroui/code instead.
    peerDependencies:
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/date-input@2.3.8':
    resolution: {integrity: sha512-phj0Y8F/GpsKjKSiratFwh7HDzmMsIf6G2L2ljgWqA79PvP+RYf/ogEfaMIq1knF8OlssMo5nsFFJNsNB+xKGg==}
    deprecated: This package has been deprecated. Please use @heroui/date-input instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/date-picker@2.3.9':
    resolution: {integrity: sha512-RzdVTl/tulTyE5fwGkQfn0is5hsTkPPRJFJZXMqYeci85uhpD+bCreWnTXrGFIXcqUo0ZBJWx3EdtBJZnGp4xQ==}
    deprecated: This package has been deprecated. Please use @heroui/date-picker instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/divider@2.0.28':
    resolution: {integrity: sha512-IskKmDOO8qwmTO2WtDmrH8fZvnV2JebP3PFfwqpToAdDRbRUs78pls2e8/T9clbLLtNxjfCFAI/Yi9C+LPPEXw==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/divider@2.2.5':
    resolution: {integrity: sha512-OB8b3CU4nQ5ARIGL48izhzrAHR0mnwws+Kd5LqRCZ/1R9uRMqsq7L0gpG9FkuV2jf2FuA7xa/GLOLKbIl4CEww==}
    deprecated: This package has been deprecated. Please use @heroui/divider instead.
    peerDependencies:
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/dom-animation@2.1.1':
    resolution: {integrity: sha512-xLrVNf1EV9zyyZjk6j3RptOvnga1WUCbMpDgJLQHp+oYwxTfBy0SkXHuN5pRdcR0XpR/IqRBDIobMdZI0iyQyg==}
    peerDependencies:
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'

  '@nextui-org/drawer@2.2.7':
    resolution: {integrity: sha512-a1Sr3sSjOZD0SiXDYSySKkOelTyCYExPvUsIckzjF5A3TNlBw4KFKnJzaXvabC3SNRy6/Ocq7oqz6VRv37wxQg==}
    deprecated: This package has been deprecated. Please use @heroui/drawer instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/dropdown@2.3.9':
    resolution: {integrity: sha512-ElZxiP+nG0CKC+tm6LMZX42cRWXQ0LLjWBZXymupPsEH3XcQpCF9GWb9efJ2hh+qGROg7i0bnFH7P0GTyCyNBA==}
    deprecated: This package has been deprecated. Please use @heroui/dropdown instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/form@2.1.8':
    resolution: {integrity: sha512-Xn/dUO5zDG7zukbql1MDYh4Xwe1vnIVMRTHgckbkBtXXVNqgoTU09TTfy8WOJ0pMDX4GrZSBAZ86o37O+IHbaA==}
    deprecated: This package has been deprecated. Please use @heroui/form instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/framer-utils@2.0.21':
    resolution: {integrity: sha512-kZzkaAHbtuBl85mivZ1WKVCcwdk8Z2NDmJiIpaLy16yliLNV1tnhoDOzRrxhv+6cbkKftx21tRrpImB4AyeqLw==}
    peerDependencies:
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/framer-utils@2.0.24':
    resolution: {integrity: sha512-Fc5ugVaLsXhd3bgJg+hvw20uaaz9gAxYY2ouS/3leN7QBSRAwpy3Dl+tX8BbLeyx3ZosVrHIJ3w4bhDMzFVk9Q==}
    peerDependencies:
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/framer-utils@2.1.6':
    resolution: {integrity: sha512-b+BxKFox8j9rNAaL+CRe2ZMb1/SKjz9Kl2eLjDSsq3q82K/Hg7lEjlpgE8cu41wIGjH1unQxtP+btiJgl067Ow==}
    peerDependencies:
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/image@2.2.5':
    resolution: {integrity: sha512-A6DnEqG+/cMrfvqFKKJIdGD7gD88tVkqGxRkfysVMJJR96sDIYCJlP1jsAEtYKh4PfhmtJWclUvY/x9fMw0H1w==}
    deprecated: This package has been deprecated. Please use @heroui/image instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/input-otp@2.1.8':
    resolution: {integrity: sha512-J5Pz0aSfWD+2cSgLTKQamCNF/qHILIj8L0lY3t1R/sgK1ApN3kDNcUGnVm6EDh+dOXITKpCfnsCQw834nxZhsg==}
    deprecated: This package has been deprecated. Please use @heroui/input-otp instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/input@2.2.2':
    resolution: {integrity: sha512-mCcFsObJdlCWMuSutKTRniFIDX5+z4BAAtt/XI1uzOtUO6WXgT97BwVzMihC1l14WQsw9TCwFKAl8JWdolkNCA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/input@2.4.8':
    resolution: {integrity: sha512-wfkjyl7vRqT3HDXeybhfZ+IAz+Z02U5EiuWPpc9NbdwhJ/LpDRDa6fYcTDr/6j6MiyrEZsM24CtZZKAKBVBquQ==}
    deprecated: This package has been deprecated. Please use @heroui/input instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/kbd@2.0.30':
    resolution: {integrity: sha512-rQw71noVUIRPf8N/Z5hdIGCtjFEVZO9xs2JVkiusKDxbGXFWKxJ3sTFzEY4VyLtORt2mEOQEWh26wbTnNjJzMw==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/kbd@2.2.6':
    resolution: {integrity: sha512-IwzvvwYLMbhyqX5PjEZyDBO4iNEHY6Nek4ZrVR+Z2dOSj/oZXHWiabNDrvOcGKgUBE6xc95Fi1jVubE9b5ueuA==}
    deprecated: This package has been deprecated. Please use @heroui/kbd instead.
    peerDependencies:
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/link@2.0.32':
    resolution: {integrity: sha512-NIG8Ay/WfFxwMYKB11xg0iVAzJR1jy0QrtKFGaZscyJ522beM+aMBZuourC9u7kwjucTvt5fuGRm86KBVDBXCQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/link@2.2.7':
    resolution: {integrity: sha512-SAeBBCUtdaKtHfZgRD6OH0De/+cKUEuThiErSuFW+sNm/y8m3cUhQH8UqVBPu6HwmqVTEjvZzp/4uhG6lcSZjA==}
    deprecated: This package has been deprecated. Please use @heroui/link instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/listbox@2.1.21':
    resolution: {integrity: sha512-mVbN9IWg9MBLwreqRR+6WiCQDu/u30KsN19e7V9TkAumxTaTqaarb34KudXz6wiObN/ORBsFnV5H3p2AtuANVA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/listbox@2.3.9':
    resolution: {integrity: sha512-iGJ8xwkXf8K7chk1iZgC05KGpHiWJXY1dnV7ytIJ7yu4BbsRIHb0QknK5j8A74YeGpouJQ9+jsmCERmySxlqlg==}
    deprecated: This package has been deprecated. Please use @heroui/listbox instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/menu@2.2.9':
    resolution: {integrity: sha512-Fztvi3GRYl5a5FO/0LRzcAdnw8Yeq6NX8yLQh8XmwkWCrH0S6nTn69CP/j+EMWQR6G2UK5AbNDmX1Sx9aTQdHQ==}
    deprecated: This package has been deprecated. Please use @heroui/menu instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/modal@2.0.39':
    resolution: {integrity: sha512-b0G5IRNrfQumx8mQQO92rn2iC2ueUuk4XKvxYYmYNpx3/qpdEP9tckozw+s0QFyZocRPY+yYa0pBtMBGC2lWGQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/modal@2.2.7':
    resolution: {integrity: sha512-xxk6B+5s8//qYI4waLjdWoJFwR6Zqym/VHFKkuZAMpNABgTB0FCK022iUdOIP2F2epG69un8zJF0qwMBJF8XAA==}
    deprecated: This package has been deprecated. Please use @heroui/modal instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/navbar@2.0.33':
    resolution: {integrity: sha512-WbPLEz6yE1vxKTqZDN85YPCWR/JSvpOO604xBpaaCf+OLfEsb+herz7+GDPnvHKaPDASoxU5WaSQJR9nrJ/YHg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/navbar@2.2.8':
    resolution: {integrity: sha512-XutioQ75jonZk6TBtjFdV6N3eLe8y85tetjOdOg6X3mKTPZlQuBb+rtb6pVNOOvcuQ7zKigWIq2ammvF9VNKaQ==}
    deprecated: This package has been deprecated. Please use @heroui/navbar instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/pagination@2.2.8':
    resolution: {integrity: sha512-sZcriQq/ssOItX3r54tysnItjcb7dw392BNulJxrMMXi6FA6sUGImpJF1jsbtYJvaq346IoZvMrcrba8PXEk0g==}
    deprecated: This package has been deprecated. Please use @heroui/pagination instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/popover@2.3.9':
    resolution: {integrity: sha512-glLYKlFJ4EkFrNMBC3ediFPpQwKzaFlzKoaMum2G3HUtmC4d1HLTSOQJOd2scUzZxD3/K9dp1XHYbEcCnCrYpQ==}
    deprecated: This package has been deprecated. Please use @heroui/popover instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/progress@2.2.6':
    resolution: {integrity: sha512-FTicOncNcXKpt9avxQWWlVATvhABKVMBgsB81SozFXRcn8QsFntjdMp0l3688DJKBY0GxT+yl/S/by0TwY1Z1A==}
    deprecated: This package has been deprecated. Please use @heroui/progress instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/radio@2.1.4':
    resolution: {integrity: sha512-Y18TXvGVz/G1E3jjYmutSSx1EdQRs5iMCVZNS/Bz4avE9QMSrHl6fOhZIndrm8LwCTqn7lbKRQngZLN4tvPinQ==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/radio@2.3.8':
    resolution: {integrity: sha512-ntwjpQ/WT8zQ3Fw5io65VeH2Q68LOgZ4lII7a6x35NDa7Eda1vlYroMAw/vxK8iyZYlUBSJdsoj2FU/10hBPmg==}
    deprecated: This package has been deprecated. Please use @heroui/radio instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.3'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/react-rsc-utils@2.0.12':
    resolution: {integrity: sha512-s2IG4pM1K+kbm6A2g3UpqrS592AExpGixtZNPJ2lV5+UQi1ld3vb4EiBIOViZMoSCNCoNdaeO5Yqo6cKghwCPA==}

  '@nextui-org/react-rsc-utils@2.0.13':
    resolution: {integrity: sha512-QewsXtoQlMsR9stThdazKEImg9oyZkPLs7wsymhrzh6/HdQCl9bTdb6tJcROg4vg5LRYKGG11USSQO2nKlfCcQ==}

  '@nextui-org/react-rsc-utils@2.1.1':
    resolution: {integrity: sha512-9uKH1XkeomTGaswqlGKt0V0ooUev8mPXtKJolR+6MnpvBUrkqngw1gUGF0bq/EcCCkks2+VOHXZqFT6x9hGkQQ==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/react-utils@2.0.13':
    resolution: {integrity: sha512-4DM1Cph1lVY64T/HDyEqcxYkInXx6hdL1Kp9StLza9yqgYmVipTaPkWZdmWbfkhP+dVVqrH3DVFfHtpLTQ625w==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/react-utils@2.0.14':
    resolution: {integrity: sha512-fed97WSaHt8/sC5F4DFTVj25YQsepFGDyudommPGQsTksQ6GQkMITuHckzAyPiTTuWHSW/GZykvVVAlK9hS5Wg==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/react-utils@2.0.16':
    resolution: {integrity: sha512-QdDoqzhx+4t9cDTVmtw5iOrfyLvpqyKsq8PARHUniCiQQDQd1ao7FCpzHgvU9poYcEdRk+Lsna66zbeMkFBB6w==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/react-utils@2.1.3':
    resolution: {integrity: sha512-o61fOS+S8p3KtgLLN7ub5gR0y7l517l9eZXJabUdnVcZzZjTqEijWjzjIIIyAtYAlL4d+WTXEOROuc32sCmbqw==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/react@2.6.11':
    resolution: {integrity: sha512-MOkBMWI+1nHB6A8YLXakdXrNRFvy5whjFJB1FthwqbP8pVEeksS1e29AbfEFkrzLc5zjN7i24wGNSJ8DKMt9WQ==}
    deprecated: This package has been deprecated. Please use @heroui/react instead.
    peerDependencies:
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/ripple@2.0.30':
    resolution: {integrity: sha512-GmHwC+F2JIYQAeFuwtFbdE6av8lzOJVdA5yops9vhhzeBPT33dMjgazCn0HZT5TvP0gX+xxT/74ONE0ik0Kayg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/ripple@2.0.32':
    resolution: {integrity: sha512-xOqoHWzpvv5KRh7P8pXt3aZEmI1tyhiTNhrwjJaRME0d5xSA0gNzYhrjP5g0+Dxy4nKRDIZ1znJcd87KI07JFA==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/ripple@2.2.7':
    resolution: {integrity: sha512-cphzlvCjdROh1JWQhO/wAsmBdlU9kv/UA2YRQS4viaWcA3zO+qOZVZ9/YZMan6LBlOLENCaE9CtV2qlzFtVpEg==}
    deprecated: This package has been deprecated. Please use @heroui/ripple instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/scroll-shadow@2.3.5':
    resolution: {integrity: sha512-2H5qro6RHcWo6ZfcG2hHZHsR1LrV3FMZP5Lkc9ZwJdWPg4dXY4erGRE4U+B7me6efj5tBOFmZkIpxVUyMBLtZg==}
    deprecated: This package has been deprecated. Please use @heroui/scroll-shadow instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/select@2.4.9':
    resolution: {integrity: sha512-R8HHKDH7dA4Dv73Pl80X7qfqdyl+Fw4gi/9bmyby0QJG8LN2zu51xyjjKphmWVkAiE3O35BRVw7vMptHnWFUgQ==}
    deprecated: This package has been deprecated. Please use @heroui/select instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/shared-icons@2.0.8':
    resolution: {integrity: sha512-siKuw+CN03cB2N1eUpIleP+lTpjM4gSmcco7RXTpXiwXJXlxjKo4N8gQYS04HCBXm9QMWgyngvUEt2II9NYyrw==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/shared-icons@2.0.9':
    resolution: {integrity: sha512-WG3yinVY7Tk9VqJgcdF4V8Ok9+fcm5ey7S1els7kujrfqLYxtqoKywgiY/7QHwZlfQkzpykAfy+NAlHkTP5hMg==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/shared-icons@2.1.1':
    resolution: {integrity: sha512-mkiTpFJnCzB2M8Dl7IwXVzDKKq9ZW2WC0DaQRs1eWgqboRCP8DDde+MJZq331hC7pfH8BC/4rxXsKECrOUUwCg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/shared-utils@2.0.5':
    resolution: {integrity: sha512-aFc/CUL8RVfBh0IotIpxkpKjyUPc/zJaMJd5pRCQA1kIpKLdSrlh3//MLYMaP/fo/NQtE3DPeXqfKhHRr1fkEw==}

  '@nextui-org/shared-utils@2.0.7':
    resolution: {integrity: sha512-FxY3N0i1Al7Oz3yOQN0dSpG8UUrLIP3iYh3ubD7BhdQoZLl5xbG6++q1gqOzZXV+ZWeUFMY/or0ofzWxGHiOow==}

  '@nextui-org/shared-utils@2.1.2':
    resolution: {integrity: sha512-5n0D+AGB4P9lMD1TxwtdRSuSY0cWgyXKO9mMU11Xl3zoHNiAz/SbCSTc4VBJdQJ7Y3qgNXvZICzf08+bnjjqqA==}

  '@nextui-org/skeleton@2.2.5':
    resolution: {integrity: sha512-CK1O9dqS0xPW3o1SIekEEOjSosJkXNzU0Zd538Nn1XhY1RjNuIPchpY9Pv5YZr2QSKy0zkwPQt/NalwErke0Jg==}
    deprecated: This package has been deprecated. Please use @heroui/skeleton instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/slider@2.4.7':
    resolution: {integrity: sha512-/RnjnmAPvssebhtElG+ZI8CCot2dEBcEjw7LrHfmVnJOd5jgceMtnXhdJSppQuLvcC4fPpkhd6dY86IezOZwfw==}
    deprecated: This package has been deprecated. Please use @heroui/slider instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/snippet@2.0.38':
    resolution: {integrity: sha512-8lMqtB1KQtMkpZFb3x/T42zdZ+QqcGr6d/yVE+zKzyEd+xqzm2g/hDpPqy0Mf5JaC1Z+lXoRzF/6XbD99FCEbw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/snippet@2.2.10':
    resolution: {integrity: sha512-mVjf8muq4TX2PlESN7EeHgFmjuz7PNhrKFP+fb8Lj9J6wvUIUDm5ENv9bs72cRsK+zse6OUNE4JF1er6HllKug==}
    deprecated: This package has been deprecated. Please use @heroui/snippet instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/spacer@2.0.32':
    resolution: {integrity: sha512-NxqEYTig4OfkLDPlO2/jASB4gV8L9DLpsNZSqzaacIJZwk4BCTsNoBi3CuNt5ZsMoGYujtFP6QU0zH9fZbuzwA==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/spacer@2.2.6':
    resolution: {integrity: sha512-1qYtZ6xICfSrFV0MMB/nUH1K2X9mHzIikrjC/okzyzWywibsVNbyRfu5vObVClYlVGY0r4M4+7fpV2QV1tKRGw==}
    deprecated: This package has been deprecated. Please use @heroui/spacer instead.
    peerDependencies:
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/spinner@2.0.30':
    resolution: {integrity: sha512-+oygL2dewHZzJiSUEIvzL0tIx+G+98mvO3ToFAMXaH0N3bOQNSiFDPwUHUx6PgAQ9pr9RKtdnb4ywstcG9j+Gg==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/spinner@2.2.6':
    resolution: {integrity: sha512-0V0H8jVpgRolgLnCuKDbrQCSK0VFPAZYiyGOE1+dfyIezpta+Nglh+uEl2sEFNh6B9Z8mARB8YEpRnTcA0ePDw==}
    deprecated: This package has been deprecated. Please use @heroui/spinner instead.
    peerDependencies:
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/switch@2.0.31':
    resolution: {integrity: sha512-WPHqWQfyISA8nmQ8ihaO5rIHm/K9nyfrV0Fxm6EcnFilTMZhh4Kt+p7FfJrZw+MMyzIEGFfMDySk1KVrMubc1g==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/switch@2.2.8':
    resolution: {integrity: sha512-wk9qQSOfUEtmdWR1omKjmEYzgMjJhVizvfW6Z0rKOiMUuSud2d4xYnUmZhU22cv2WtoPV//kBjXkYD/E/t6rdg==}
    deprecated: This package has been deprecated. Please use @heroui/switch instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.3'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/system-rsc@2.1.2':
    resolution: {integrity: sha512-3F7pG68Ikh1JsMtRQqmyXAojAV4lMPCKCy0n8RiIxJkEJg11RGTXhnABHF2jP6uxMH/0q5zVzuFubQJfW++ISQ==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'

  '@nextui-org/system-rsc@2.1.5':
    resolution: {integrity: sha512-tkJLAyJu34Rr5KUMMqoB7cZjOVXB+7a/7N4ushZfuiLdoYijgmcXFMzLxjm+tbt9zA5AV+ivsfbHvscg77dJ6w==}
    peerDependencies:
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'

  '@nextui-org/system-rsc@2.3.5':
    resolution: {integrity: sha512-DpVLNV9LkeP1yDULFCXm2mxA9m4ygS7XYy3lwgcF9M1A8QAWB+ut+FcP+8a6va50oSHOqwvUwPDUslgXTPMBfQ==}
    peerDependencies:
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/system@2.2.1':
    resolution: {integrity: sha512-XBNf+8cB1S0OOfVg5jOCtcaL0yWCYT9M6N+MGLI9JSyvPlpN1eCsoy0qdW3yI124ECpkZD0xbqbeXvvQsEdkKQ==}
    peerDependencies:
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/system@2.2.2':
    resolution: {integrity: sha512-u30lWSIO4Q7DStiK5tJjDgKBQtmODeQZcC6llz973sJ9QlE4GeC1fgu0+/zXL8AZZ8o/iEXhHWXsZIJ26EquUQ==}
    peerDependencies:
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/system@2.2.5':
    resolution: {integrity: sha512-nrX6768aiyWtpxX3OTFBIVWR+v9nlMsC3KaBinNfek97sNm7gAfTHi7q5kylE3L5yIMpNG+DclAKpuxgDQEmvw==}
    peerDependencies:
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/system@2.4.6':
    resolution: {integrity: sha512-6ujAriBZMfQ16n6M6Ad9g32KJUa1CzqIVaHN/tymadr/3m8hrr7xDw6z50pVjpCRq2PaaA1hT8Hx7EFU3f2z3Q==}
    peerDependencies:
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/table@2.0.39':
    resolution: {integrity: sha512-VYvmrQ6GliwmzukKLZ7Nxp3sFXdskWZp8/BjwROLFE9Zco22CC0++7VPG3ebOYAIhi4e1Je+QUTx4/eh2wZZgg==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/table@2.2.8':
    resolution: {integrity: sha512-XNM0/Ed7Re3BA1eHL31rzALea9hgsBwD0rMR2qB2SAl2e8KaV2o+4bzgYhpISAzHQtlG8IsXanxiuNDH8OPVyw==}
    deprecated: This package has been deprecated. Please use @heroui/table instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/tabs@2.2.7':
    resolution: {integrity: sha512-EDPK0MOR4DPTfud9Khr5AikLbyEhHTlkGfazbOxg7wFaHysOnV5Y/E6UfvaN69kgIeT7NQcDFdaCKJ/AX1N7AA==}
    deprecated: This package has been deprecated. Please use @heroui/tabs instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/theme@2.2.5':
    resolution: {integrity: sha512-xvlMUK/rTv95s9hvr/XWtyVTm3IHRR7gCHpGOhG9DsxkQFbSQDiznsBC7LPBFJ21o+idiv28Il6aroV3ua/BOA==}
    peerDependencies:
      tailwindcss: '>=3.4.0'

  '@nextui-org/theme@2.4.5':
    resolution: {integrity: sha512-c7Y17n+hBGiFedxMKfg7Qyv93iY5MteamLXV4Po4c1VF1qZJI6I+IKULFh3FxPWzAoz96r6NdYT7OLFjrAJdWg==}
    peerDependencies:
      tailwindcss: '>=3.4.0'

  '@nextui-org/tooltip@2.0.36':
    resolution: {integrity: sha512-tV3BefTvmYzSC4TX+UPV7p3F5fs52sFzQ1/Try/Bkz5B1F9yXviO9dV2/pqXSfOJVvLVJS2RMi5wZkaYh1xtNw==}
    peerDependencies:
      '@nextui-org/system': '>=2.0.0'
      '@nextui-org/theme': '>=2.1.0'
      framer-motion: '>=10.17.0'
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/tooltip@2.2.7':
    resolution: {integrity: sha512-NgoaxcNwuCq/jvp77dmGzyS7JxzX4dvD/lAYi/GUhyxEC3TK3teZ3ADRhrC6tb84OpaelPLaTkhRNSaxVAQzjQ==}
    deprecated: This package has been deprecated. Please use @heroui/tooltip instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      framer-motion: '>=11.5.6 || >=12.0.0-alpha.1'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-aria-accordion@2.2.2':
    resolution: {integrity: sha512-M8gjX6XmB83cIAZKV2zI1KvmTuuOh+Si50F3SWvYjBXyrDIM5775xCs2PG6AcLjf6OONTl5KwuZ2cbSDHiui6A==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-aria-button@2.0.10':
    resolution: {integrity: sha512-tUpp4QMr1zugKPevyToeRHIufTuc/g+67/r/oQLRTG0mMo3yGVmggykQuYn22fqqZPpW6nHcB9VYc+XtZZ27TQ==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-aria-button@2.0.9':
    resolution: {integrity: sha512-5FjDl57/1Ey3MgJn+yB0/CPABsSVgXiE+jT7ZLnSqH9kmdXV/eMiuplF7fOOvaSMCA1cE3KCetaPVDIZoJI1/w==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-aria-button@2.2.4':
    resolution: {integrity: sha512-Bz8l4JGzRKh6V58VX8Laq4rKZDppsnVuNCBHpMJuLo2F9ht7UKvZAEJwXcdbUZ87aui/ZC+IPYqgjvT+d8QlQg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-aria-link@2.0.18':
    resolution: {integrity: sha512-6ZIIOfMMGbSOF9FcJTPrsVOm2LP7OV+QwF0vYelZeEK5zFXb5f8e2J/fEbCVWKLPFDB2VyoBUDWMzRfrizixzg==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-aria-link@2.2.5':
    resolution: {integrity: sha512-LBWXLecvuET4ZcpoHyyuS3yxvCzXdkmFcODhYwUmC8PiFSEUHkuFMC+fLwdXCP5GOqrv6wTGYHf41wNy1ugX1w==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-aria-modal-overlay@2.0.11':
    resolution: {integrity: sha512-crMOCHyGIiBJiihxqidJCNR3AHH62uewfImDLEwyE/SlIkhAqW5jteUhkq0QfCSH4U/ydWisQ14niWDEgtzxXg==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'

  '@nextui-org/use-aria-modal-overlay@2.2.3':
    resolution: {integrity: sha512-55DIVY0u+Ynxy1/DtzZkMsdVW63wC0mafKXACwCi0xV64D0Ggi9MM7BRePLK0mOboSb3gjCwYqn12gmRiy+kmg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-aria-multiselect@2.4.3':
    resolution: {integrity: sha512-PwDA4Y5DOx0SMxc277JeZi8tMtaINTwthPhk8SaDrtOBhP+r9owS3T/W9t37xKnmrTerHwaEq4ADGQtm5/VMXQ==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-aria-toggle-button@2.0.9':
    resolution: {integrity: sha512-JpPD97tYpPwyhgXgJbWYgMDp5ZysM1LyvvmyHmq6BtvSpyYqQKU7V3LDXuirBEN6NwHHZRfXy4/mUid/L6W0wA==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-callback-ref@2.0.6':
    resolution: {integrity: sha512-2WcwWuK1L/wIpTbibnLrysmmkzWomvkVIcgWayB6n/w+bpPrPCG7Zyg2WHzmMmDhe6imV//KKBgNKRi8Xhu/VA==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-callback-ref@2.1.1':
    resolution: {integrity: sha512-DzlKJ9p7Tm0x3HGjynZ/CgS1jfoBILXKFXnYPLr/SSETXqVaCguixolT/07BRB1yo9AGwELaCEt91BeI0Rb6hQ==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-clipboard@2.0.5':
    resolution: {integrity: sha512-1ExwXM8ENmc/kVDqKoiPGrBP/0B7rZ43iSv2MoWD1Qpc8GHg71Rv7NTIlBDoD/pfUfqkab6x66iKC7AVR8rifA==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-clipboard@2.1.2':
    resolution: {integrity: sha512-MUITEPaQAvu9VuMCUQXMc4j3uBgXoD8LVcuuvUVucg/8HK/Xia0dQ4QgK30QlCbZ/BwZ047rgMAgpMZeVKw4MQ==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-data-scroll-overflow@2.2.2':
    resolution: {integrity: sha512-TFB6BuaLOsE++K1UEIPR9StkBgj9Cvvc+ccETYpmn62B7pK44DmxjkwhK0ei59wafJPIyytZ3DgdVDblfSyIXA==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-disclosure@2.0.10':
    resolution: {integrity: sha512-s2I58d7x2f1JRriZnNm9ZoxrGmxF+DnC9BXM1sD99Wq1VNMd0dhitmx0mUWfUB7l5HLyZgKOeiSLG+ugy1F1Yw==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-disclosure@2.2.2':
    resolution: {integrity: sha512-ka+5Fic2MIYtOMHi3zomtkWxCWydmJmcq7+fb6RHspfr0tGYjXWYO/lgtGeHFR1LYksMPLID3c7shT5bqzxJcA==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-draggable@2.1.2':
    resolution: {integrity: sha512-gN4G42uuRyFlAZ3FgMSeZLBg3LIeGlKTOLRe3JvyaBn1D1mA2+I3XONY1oKd9KKmtYCJNwY/2x6MVsBfy8nsgw==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-image@2.1.2':
    resolution: {integrity: sha512-I46M5gCJK4rZ0qYHPx3kVSF2M2uGaWPwzb3w4Cmx8K9QS+LbUQtRMbD8KOGTHZGA3kBDPvFbAi53Ert4eACrZQ==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-intersection-observer@2.2.2':
    resolution: {integrity: sha512-fS/4m8jnXO7GYpnp/Lp+7bfBEAXPzqsXgqGK6qrp7sfFEAbLzuJp0fONkbIB3F6F3FJrbFOlY+Y5qrHptO7U/Q==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-is-mobile@2.0.7':
    resolution: {integrity: sha512-BmOseC8Xmp5Xl8EKrsl/MoYtz0aIkezMatYGBCoGDGUosaKx8kNYv6T2WVA3uKj1Gr3s4dHhMCuISvcpE9XOiQ==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-is-mobile@2.2.2':
    resolution: {integrity: sha512-gcmUL17fhgGdu8JfXF12FZCGATJIATxV4jSql+FNhR+gc+QRRWBRmCJSpMIE2RvGXL777tDvvoh/tjFMB3pW4w==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-is-mounted@2.1.1':
    resolution: {integrity: sha512-osJB3E/DCu4Le0f+pb21ia9/TaSHwme4r0fHjO5/nUBYk/RCvGlRUUCJClf/wi9WfH8QyjuJ27+zBcUSm6AMMg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-measure@2.0.1':
    resolution: {integrity: sha512-uEtdrdBdFz4Fgbfk2vmQ+rEb+eFa5o4yI90udasvfpaIrMBfrFOlRW5+yn3uXKB8JThET4Gf2on/wlJpo567Dg==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-measure@2.0.2':
    resolution: {integrity: sha512-H/RSPPA9B5sZ10wiXR3jLlYFEuiVnc0O/sgLLQfrb5M0hvHoaqMThnsZpm//5iyS7tD7kxPeYNLa1EhzlQKxDA==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-measure@2.1.1':
    resolution: {integrity: sha512-2RVn90gXHTgt6fvzBH4fzgv3hMDz+SEJkqaCTbd6WUNWag4AaLb2WU/65CtLcexyu10HrgYf2xG07ZqtJv0zSg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-pagination@2.2.3':
    resolution: {integrity: sha512-V2WGIq4LLkTpq6EUhJg3MVvHY2ZJ63AYV9N0d52Dc3Qqok0tTRuY51dd1P+F58HyTPW84W2z4q2R8XALtzFxQw==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-safe-layout-effect@2.0.5':
    resolution: {integrity: sha512-YQQlqz82aYxMoEq23jQNG/JBPHF1x3opzyXRHAVxgBEFo9OJqBMZTm23ukpTXm2Ev98T6mpWiTHdfyHJ7IoRog==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-safe-layout-effect@2.0.6':
    resolution: {integrity: sha512-xzEJXf/g9GaSqjLpQ4+Z2/pw1GPq2Fc5cWRGqEXbGauEMXuH8UboRls1BmIV1RuOpqI6FgxkEmxL1EuVIRVmvQ==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-safe-layout-effect@2.1.1':
    resolution: {integrity: sha512-p0vezi2eujC3rxlMQmCLQlc8CNbp+GQgk6YcSm7Rk10isWVlUII5T1L3y+rcFYdgTPObCkCngPPciNQhD7Lf7g==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-scroll-position@2.0.6':
    resolution: {integrity: sha512-dRwew37XnJOh8d35BuyqzRfnrmKsOUHqi0Owhk0tIGyqifQ/jw65udWpBfa6rwXcd4cKOOqXXHuNGsYTclzc6w==}
    peerDependencies:
      react: '>=18'

  '@nextui-org/use-scroll-position@2.1.1':
    resolution: {integrity: sha512-RgY1l2POZbSjnEirW51gdb8yNPuQXHqJx3TS8Ut5dk+bhaX9JD3sUdEiJNb3qoHAJInzyjN+27hxnACSlW0gzg==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/use-update-effect@2.1.1':
    resolution: {integrity: sha512-fKODihHLWcvDk1Sm8xDua9zjdbstxTOw9shB7k/mPkeR3E7SouSpN0+LW67Bczh1EmbRg1pIrFpEOLnbpgMFzA==}
    peerDependencies:
      react: '>=18 || >=19.0.0-rc.0'

  '@nextui-org/user@2.2.6':
    resolution: {integrity: sha512-iimFoP3DVK85p78r0ekC7xpVPQiBIbWnyBPdrnBj1UEgQdKoUzGhVbhYUnA8niBz/AS5xLt6aQixsv9/B0/msw==}
    deprecated: This package has been deprecated. Please use @heroui/user instead.
    peerDependencies:
      '@nextui-org/system': '>=2.4.0'
      '@nextui-org/theme': '>=2.4.0'
      react: '>=18 || >=19.0.0-rc.0'
      react-dom: '>=18 || >=19.0.0-rc.0'

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@nolyfill/is-core-module@1.0.39':
    resolution: {integrity: sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==}
    engines: {node: '>=12.4.0'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@react-aria/breadcrumbs@3.5.19':
    resolution: {integrity: sha512-mVngOPFYVVhec89rf/CiYQGTfaLRfHFtX+JQwY7sNYNqSA+gO8p4lNARe3Be6bJPgH+LUQuruIY9/ZDL6LT3HA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/button@3.11.0':
    resolution: {integrity: sha512-b37eIV6IW11KmNIAm65F3SEl2/mgj5BrHIysW6smZX3KoKWTGYsYfcQkmtNgY0GOSFfDxMCoolsZ6mxC00nSDA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/button@3.9.5':
    resolution: {integrity: sha512-dgcYR6j8WDOMLKuVrtxzx4jIC05cVKDzc+HnPO8lNkBAOfjcuN5tkGRtIjLtqjMvpZHhQT5aDbgFpIaZzxgFIg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/calendar@3.6.0':
    resolution: {integrity: sha512-tZ3nd5DP8uxckbj83Pt+4RqgcTWDlGi7njzc7QqFOG2ApfnYDUXbIpb/Q4KY6JNlJskG8q33wo0XfOwNy8J+eg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/checkbox@3.14.3':
    resolution: {integrity: sha512-EtBJL6iu0gvrw3A4R7UeVLR6diaVk/mh4kFBc7c8hQjpEJweRr4hmJT3hrNg3MBcTWLxFiMEXPGgWEwXDBygtA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/checkbox@3.15.0':
    resolution: {integrity: sha512-z/8xd4em7o0MroBXwkkwv7QRwiJaA1FwqMhRUb7iqtBGP2oSytBEDf0N7L09oci32a1P4ZPz2rMK5GlLh/PD6g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/combobox@3.11.0':
    resolution: {integrity: sha512-s88YMmPkMO1WSoiH1KIyZDLJqUwvM2wHXXakj3cYw1tBHGo4rOUFq+JWQIbM5EDO4HOR4AUUqzIUd0NO7t3zyg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/datepicker@3.12.0':
    resolution: {integrity: sha512-VYNXioLfddIHpwQx211+rTYuunDmI7VHWBRetCpH3loIsVFuhFSRchTQpclAzxolO3g0vO7pMVj9VYt7Swp6kg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/dialog@3.5.14':
    resolution: {integrity: sha512-oqDCjQ8hxe3GStf48XWBf2CliEnxlR9GgSYPHJPUc69WBj68D9rVcCW3kogJnLAnwIyf3FnzbX4wSjvUa88sAQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/dialog@3.5.20':
    resolution: {integrity: sha512-l0GZVLgeOd3kL3Yj8xQW7wN3gn9WW3RLd/SGI9t7ciTq+I/FhftjXCWzXLlOCCTLMf+gv7eazecECtmoWUaZWQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/focus@3.16.2':
    resolution: {integrity: sha512-Rqo9ummmgotESfypzFjI3uh58yMpL+E+lJBbQuXkBM0u0cU2YYzu0uOrFrq3zcHk997udZvq1pGK/R+2xk9B7g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/focus@3.17.1':
    resolution: {integrity: sha512-FLTySoSNqX++u0nWZJPPN5etXY0WBxaIe/YuL/GTEeuqUIuC/2bJSaw5hlsM6T2yjy6Y/VAxBcKSdAFUlU6njQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/focus@3.18.2':
    resolution: {integrity: sha512-Jc/IY+StjA3uqN73o6txKQ527RFU7gnG5crEl5Xy3V+gbYp2O5L3ezAo/E0Ipi2cyMbG6T5Iit1IDs7hcGu8aw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/focus@3.19.0':
    resolution: {integrity: sha512-hPF9EXoUQeQl1Y21/rbV2H4FdUR2v+4/I0/vB+8U3bT1CJ+1AFj1hc/rqx2DqEwDlEwOHN+E4+mRahQmlybq0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/focus@3.20.2':
    resolution: {integrity: sha512-Q3rouk/rzoF/3TuH6FzoAIKrl+kzZi9LHmr8S5EqLAOyP9TXIKG34x2j42dZsAhrw7TbF9gA8tBKwnCNH4ZV+Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/form@3.0.11':
    resolution: {integrity: sha512-oXzjTiwVuuWjZ8muU0hp3BrDH5qjVctLOF50mjPvqUbvXQTHhoDxWweyIXPQjGshaqBd2w4pWaE4A2rG2O/apw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/form@3.0.15':
    resolution: {integrity: sha512-kk8AnLz+EOgnn3sTaXYmtw+YzVDc1of/+xAkuOupQi6zQFnNRjc99JlDbKHoUZ39urMl+8lsp/1b9VPPhNrBNw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/form@3.0.8':
    resolution: {integrity: sha512-8S2QiyUdAgK43M3flohI0R+2rTyzH088EmgeRArA8euvJTL16cj/oSOKMEgWVihjotJ9n6awPb43ZhKboyNsMg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/grid@3.10.3':
    resolution: {integrity: sha512-l0r9mz05Gwjq3t6JOTNQOf+oAoWN0bXELPJtIr8m0XyXMPFCQe1xsTaX8igVQdrDmXyBc75RAWS0BJo2JF2fIA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/grid@3.13.0':
    resolution: {integrity: sha512-RcuJYA4fyJ83MH3SunU+P5BGkx3LJdQ6kxwqwWGIuI9eUKc7uVbqvN9WN3fI+L0QfxqBFmh7ffRxIdQn7puuzw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/i18n@3.10.2':
    resolution: {integrity: sha512-Z1ormoIvMOI4mEdcFLYsoJy9w/EzBdBmgfLP+S/Ah+1xwQOXpgwZxiKOhYHpWa0lf6hkKJL34N9MHJvCJ5Crvw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/i18n@3.11.1':
    resolution: {integrity: sha512-vuiBHw1kZruNMYeKkTGGnmPyMnM5T+gT8bz97H1FqIq1hQ6OPzmtBZ6W6l6OIMjeHI5oJo4utTwfZl495GALFQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/i18n@3.12.2':
    resolution: {integrity: sha512-PvEyC6JWylTpe8dQEWqQwV6GiA+pbTxHQd//BxtMSapRW3JT9obObAnb/nFhj3HthkUvqHyj0oO1bfeN+mtD8A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/i18n@3.12.4':
    resolution: {integrity: sha512-j9+UL3q0Ls8MhXV9gtnKlyozq4aM95YywXqnmJtzT1rYeBx7w28hooqrWkCYLfqr4OIryv1KUnPiCSLwC2OC7w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/i18n@3.12.8':
    resolution: {integrity: sha512-V/Nau9WuwTwxfFffQL4URyKyY2HhRlu9zmzkF2Hw/j5KmEQemD+9jfaLueG2CJu85lYL06JrZXUdnhZgKnqMkA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/interactions@3.21.1':
    resolution: {integrity: sha512-AlHf5SOzsShkHfV8GLLk3v9lEmYqYHURKcXWue0JdYbmquMRkUsf/+Tjl1+zHVAQ8lKqRnPYbTmc4AcZbqxltw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/interactions@3.21.3':
    resolution: {integrity: sha512-BWIuf4qCs5FreDJ9AguawLVS0lV9UU+sK4CCnbCNNmYqOWY+1+gRXCsnOM32K+oMESBxilAjdHW5n1hsMqYMpA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/interactions@3.22.2':
    resolution: {integrity: sha512-xE/77fRVSlqHp2sfkrMeNLrqf2amF/RyuAS6T5oDJemRSgYM3UoxTbWjucPhfnoW7r32pFPHHgz4lbdX8xqD/g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/interactions@3.22.5':
    resolution: {integrity: sha512-kMwiAD9E0TQp+XNnOs13yVJghiy8ET8L0cbkeuTgNI96sOAp/63EJ1FSrDf17iD8sdjt41LafwX/dKXW9nCcLQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/interactions@3.25.0':
    resolution: {integrity: sha512-GgIsDLlO8rDU/nFn6DfsbP9rfnzhm8QFjZkB9K9+r+MTSCn7bMntiWQgMM+5O6BiA8d7C7x4zuN4bZtc0RBdXQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/label@3.7.11':
    resolution: {integrity: sha512-REgejE5Qr8cXG/b8H2GhzQmjQlII/0xQW/4eDzydskaTLvA7lF5HoJUE6biYTquH5va38d8XlH465RPk+bvHzA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/label@3.7.13':
    resolution: {integrity: sha512-brSAXZVTey5RG/Ex6mTrV/9IhGSQFU4Al34qmjEDho+Z2qT4oPwf8k7TRXWWqzOU0ugYxekYbsLd2zlN3XvWcg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/label@3.7.17':
    resolution: {integrity: sha512-Fz7IC2LQT2Y/sAoV+gFEXoULtkznzmK2MmeTv5shTNjeTxzB1BhQbD4wyCypi7eGsnD/9Zy+8viULCsIUbvjWw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/link@3.7.1':
    resolution: {integrity: sha512-a4IaV50P3fXc7DQvEIPYkJJv26JknFbRzFT5MJOMgtzuhyJoQdILEUK6XHYjcSSNCA7uLgzpojArVk5Hz3lCpw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/link@3.7.7':
    resolution: {integrity: sha512-eVBRcHKhNSsATYWv5wRnZXRqPVcKAWWakyvfrYePIKpC3s4BaHZyTGYdefk8ZwZdEOuQZBqLMnjW80q1uhtkuA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/link@3.8.0':
    resolution: {integrity: sha512-gpDD6t3FqtFR9QjSIKNpmSR3tS4JG2anVKx2wixuRDHO6Ddexxv4SBzsE1+230p+FlFGjftFa2lEgQ7RNjZrmA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/listbox@3.11.5':
    resolution: {integrity: sha512-y3a3zQYjT+JKgugCMMKS7K9sRoCoP1Z6Fiiyfd77OHXWzh9RlnvWGsseljynmbxLzSuPwFtCYkU1Jz4QwsPUIg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/listbox@3.13.6':
    resolution: {integrity: sha512-6hEXEXIZVau9lgBZ4VVjFR3JnGU+fJaPmV3HP0UZ2ucUptfG0MZo24cn+ZQJsWiuaCfNFv5b8qribiv+BcO+Kg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/listbox@3.14.3':
    resolution: {integrity: sha512-wzelam1KENUvKjsTq8gfrOW2/iab8SyIaSXfFvGmWW82XlDTlW+oQeA39tvOZktMVGspr+xp8FySY09rtz6UXw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/live-announcer@3.3.4':
    resolution: {integrity: sha512-w8lxs35QrRrn6pBNzVfyGOeqWdxeVKf9U6bXIVwhq7rrTqRULL8jqy8RJIMfIs1s8G5FpwWYjyBOjl2g5Cu1iA==}

  '@react-aria/live-announcer@3.4.2':
    resolution: {integrity: sha512-6+yNF9ZrZ4YJ60Oxy2gKI4/xy6WUv1iePDCFJkgpNVuOEYi8W8czff8ctXu/RPB25OJx5v2sCw9VirRogTo2zA==}

  '@react-aria/menu@3.16.0':
    resolution: {integrity: sha512-TNk+Vd3TbpBPUxEloAdHRTaRxf9JBK7YmkHYiq0Yj5Lc22KS0E2eTyhpPM9xJvEWN2TlC5TEvNfdyui2kYWFFQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/menu@3.18.2':
    resolution: {integrity: sha512-90k+Ke1bhFWhR2zuRI6OwKWQrCpOD99n+9jhG96JZJZlNo5lB+5kS+ufG1LRv5GBnCug0ciLQmPMAfguVsCjEQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/overlays@3.21.1':
    resolution: {integrity: sha512-djEBDF+TbIIOHWWNpdm19+z8xtY8U+T+wKVQg/UZ6oWnclSqSWeGl70vu73Cg4HVBJ4hKf1SRx4Z/RN6VvH4Yw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/overlays@3.22.1':
    resolution: {integrity: sha512-GHiFMWO4EQ6+j6b5QCnNoOYiyx1Gk8ZiwLzzglCI4q1NY5AG2EAmfU4Z1+Gtrf2S5Y0zHbumC7rs9GnPoGLUYg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/overlays@3.23.2':
    resolution: {integrity: sha512-vjlplr953YAuJfHiP4O+CyrTlr6OaFgXAGrzWq4MVMjnpV/PT5VRJWYFHR0sUGlHTPqeKS4NZbi/xCSgl/3pGQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/overlays@3.24.0':
    resolution: {integrity: sha512-0kAXBsMNTc/a3M07tK9Cdt/ea8CxTAEJ223g8YgqImlmoBBYAL7dl5G01IOj67TM64uWPTmZrOklBchHWgEm3A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/overlays@3.27.0':
    resolution: {integrity: sha512-2vZVgL7FrloN5Rh8sAhadGADJbuWg69DdSJB3fd2/h5VvcEhnIfNPu9Ma5XmdkApDoTboIEsKZ4QLYwRl98w6w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/progress@3.4.18':
    resolution: {integrity: sha512-FOLgJ9t9i1u3oAAimybJG6r7/soNPBnJfWo4Yr6MmaUv90qVGa1h6kiuM5m9H/bm5JobAebhdfHit9lFlgsCmg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/radio@3.10.10':
    resolution: {integrity: sha512-NVdeOVrsrHgSfwL2jWCCXFsWZb+RMRZErj5vthHQW4nkHECGOzeX56VaLWTSvdoCPqi9wdIX8A6K9peeAIgxzA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/radio@3.10.4':
    resolution: {integrity: sha512-3fmoMcQtCpgjTwJReFjnvIE/C7zOZeCeWUn4JKDqz9s1ILYsC3Rk5zZ4q66tFn6v+IQnecrKT52wH6+hlVLwTA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/selection@3.19.3':
    resolution: {integrity: sha512-GYoObXCXlmGK08hp7Qfl6Bk0U+bKP5YDWSsX+MzNjJsqzQSLm4S06tRB9ACM7gIo9dDCvL4IRxdSYTJAlJc6bw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/selection@3.21.0':
    resolution: {integrity: sha512-52JJ6hlPcM+gt0VV3DBmz6Kj1YAJr13TfutrKfGWcK36LvNCBm1j0N+TDqbdnlp8Nue6w0+5FIwZq44XPYiBGg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/selection@3.24.0':
    resolution: {integrity: sha512-RfGXVc04zz41NVIW89/a3quURZ4LT/GJLkiajQK2VjhisidPdrAWkcfjjWJj0n+tm5gPWbi9Rs5R/Rc8mrvq8Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/slider@3.7.14':
    resolution: {integrity: sha512-7rOiKjLkEZ0j7mPMlwrqivc+K4OSfL14slaQp06GHRiJkhiWXh2/drPe15hgNq55HmBQBpA0umKMkJcqVgmXPA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/spinbutton@3.6.14':
    resolution: {integrity: sha512-oSKe9p0Q/7W39eXRnLxlwJG5dQo4ffosRT3u2AtOcFkk2Zzj+tSQFzHQ4202nrWdzRnQ2KLTgUUNnUvXf0BJcg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/ssr@3.9.5':
    resolution: {integrity: sha512-xEwGKoysu+oXulibNUSkXf8itW0npHHTa6c4AyYeZIJyRoegeteYuFpZUBPtIDE8RfHdNsSmE1ssOkxRnwbkuQ==}
    engines: {node: '>= 12'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/ssr@3.9.7':
    resolution: {integrity: sha512-GQygZaGlmYjmYM+tiNBA5C6acmiDWF52Nqd40bBp0Znk4M4hP+LTmI0lpI1BuKMw45T8RIhrAsICIfKwZvi2Gg==}
    engines: {node: '>= 12'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/ssr@3.9.8':
    resolution: {integrity: sha512-lQDE/c9uTfBSDOjaZUJS8xP2jCKVk4zjQeIlCH90xaLhHDgbpCdns3xvFpJJujfj3nI4Ll9K7A+ONUBDCASOuw==}
    engines: {node: '>= 12'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/switch@3.6.10':
    resolution: {integrity: sha512-FtaI9WaEP1tAmra1sYlAkYXg9x75P5UtgY8pSbe9+1WRyWbuE1QZT+RNCTi3IU4fZ7iJQmXH6+VaMyzPlSUagw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/switch@3.6.4':
    resolution: {integrity: sha512-2nVqz4ZuJyof47IpGSt3oZRmp+EdS8wzeDYgf42WHQXrx4uEOk1mdLJ20+NnsYhj/2NHZsvXVrjBeKMjlMs+0w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/table@3.14.1':
    resolution: {integrity: sha512-WaPgQe4zQF5OaluO5rm+Y2nEoFR63vsLd4BT4yjK1uaFhKhDY2Zk+1SCVQvBLLKS4WK9dhP05nrNzT0vp/ZPOw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/table@3.16.0':
    resolution: {integrity: sha512-9xF9S3CJ7XRiiK92hsIKxPedD0kgcQWwqTMtj3IBynpQ4vsnRiW3YNIzrn9C3apjknRZDTSta8O2QPYCUMmw2A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/tabs@3.9.8':
    resolution: {integrity: sha512-Nur/qRFBe+Zrt4xcCJV/ULXCS3Mlae+B89bp1Gl20vSDqk6uaPtGk+cS5k03eugOvas7AQapqNJsJgKd66TChw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/textfield@3.14.5':
    resolution: {integrity: sha512-hj7H+66BjB1iTKKaFXwSZBZg88YT+wZboEXZ0DNdQB2ytzoz/g045wBItUuNi4ZjXI3P+0AOZznVMYadWBAmiA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/textfield@3.15.0':
    resolution: {integrity: sha512-V5mg7y1OR6WXYHdhhm4FC7QyGc9TideVRDFij1SdOJrIo5IFB7lvwpOS0GmgwkVbtr71PTRMjZnNbrJUFU6VNA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/textfield@3.17.2':
    resolution: {integrity: sha512-4KINB0HueYUHUgvi/ThTP27hu4Mv5ujG55pH3dmSRD4Olu/MRy1m/Psq72o8LTf4bTOM9ZP1rKccUg6xfaMidA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/toggle@3.10.7':
    resolution: {integrity: sha512-/RJQU8QlPZXRElZ3Tt10F5K5STgUBUGPpfuFUGuwF3Kw3GpPxYsA1YAVjxXz2MMGwS0+y6+U/J1xIs1AF0Jwzg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/toggle@3.11.2':
    resolution: {integrity: sha512-JOg8yYYCjLDnEpuggPo9GyXFaT/B238d3R8i/xQ6KLelpi3fXdJuZlFD6n9NQp3DJbE8Wj+wM5/VFFAi3cISpw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/toolbar@3.0.0-beta.11':
    resolution: {integrity: sha512-LM3jTRFNDgoEpoL568WaiuqiVM7eynSQLJis1hV0vlVnhTd7M7kzt7zoOjzxVb5Uapz02uCp1Fsm4wQMz09qwQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/tooltip@3.7.10':
    resolution: {integrity: sha512-Udi3XOnrF/SYIz72jw9bgB74MG/yCOzF5pozHj2FH2HiJlchYv/b6rHByV/77IZemdlkmL/uugrv/7raPLSlnw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/tooltip@3.7.4':
    resolution: {integrity: sha512-+XRx4HlLYqWY3fB8Z60bQi/rbWDIGlFUtXYbtoa1J+EyRWfhpvsYImP8qeeNO/vgjUtDy1j9oKa8p6App9mBMQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/utils@3.24.1':
    resolution: {integrity: sha512-O3s9qhPMd6n42x9sKeJ3lhu5V1Tlnzhu6Yk8QOvDuXf7UGuUjXf9mzfHJt1dYzID4l9Fwm8toczBzPM9t0jc8Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/utils@3.25.2':
    resolution: {integrity: sha512-GdIvG8GBJJZygB4L2QJP1Gabyn2mjFsha73I2wSe+o4DYeGWoJiMZRM06PyTIxLH4S7Sn7eVDtsSBfkc2VY/NA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/utils@3.26.0':
    resolution: {integrity: sha512-LkZouGSjjQ0rEqo4XJosS4L3YC/zzQkfRM3KoqK6fUOmUJ9t0jQ09WjiF+uOoG9u+p30AVg3TrZRUWmoTS+koQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/utils@3.28.2':
    resolution: {integrity: sha512-J8CcLbvnQgiBn54eeEvQQbIOfBF3A1QizxMw9P4cl9MkeR03ug7RnjTIdJY/n2p7t59kLeAB3tqiczhcj+Oi5w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/visually-hidden@3.8.12':
    resolution: {integrity: sha512-Bawm+2Cmw3Xrlr7ARzl2RLtKh0lNUdJ0eNqzWcyx4c0VHUAWtThmH5l+HRqFUGzzutFZVo89SAy40BAbd0gjVw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-aria/visually-hidden@3.8.15':
    resolution: {integrity: sha512-l+sJ7xTdD5Sd6+rDNDaeJCSPnHOsI+BaJyApvb/YcVgHa7rB47lp6TXCWUCDItcPY4JqRGyeByRJVrtzBFTWCw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-aria/visually-hidden@3.8.18':
    resolution: {integrity: sha512-l/0igp+uub/salP35SsNWq5mGmg3G5F5QMS1gDZ8p28n7CgjvzyiGhJbbca7Oxvaw1HRFzVl9ev+89I7moNnFQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/visually-hidden@3.8.22':
    resolution: {integrity: sha512-EO3R8YTKZ7HkLl9k1Y2uBKYBgpJagth4/4W7mfpJZE24A3fQnCP8zx1sweXiAm0mirR4J6tNaK7Ia8ssP5TpOw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/calendar@3.6.0':
    resolution: {integrity: sha512-GqUtOtGnwWjtNrJud8nY/ywI4VBP5byToNVRTnxbMl+gYO1Qe/uc5NG7zjwMxhb2kqSBHZFdkF0DXVqG2Ul+BA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/checkbox@3.6.10':
    resolution: {integrity: sha512-LHm7i4YI8A/RdgWAuADrnSAYIaYYpQeZqsp1a03Og0pJHAlZL0ymN3y2IFwbZueY0rnfM+yF+kWNXjJqbKrFEQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/checkbox@3.6.5':
    resolution: {integrity: sha512-IXV3f9k+LtmfQLE+DKIN41Q5QB/YBLDCB1YVx5PEdRp52S9+EACD5683rjVm8NVRDwjMi2SP6RnFRk7fVb5Azg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/collections@3.10.5':
    resolution: {integrity: sha512-k8Q29Nnvb7iAia1QvTanZsrWP2aqVNBy/1SlE6kLL6vDqtKZC+Esd1SDLHRmIcYIp5aTdfwIGd0NuiRQA7a81Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/collections@3.10.7':
    resolution: {integrity: sha512-KRo5O2MWVL8n3aiqb+XR3vP6akmHLhLWYZEmPKjIv0ghQaEebBTrN3wiEjtd6dzllv0QqcWvDLM1LntNfJ2TsA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/collections@3.10.9':
    resolution: {integrity: sha512-plyrng6hOQMG8LrjArMA6ts/DgWyXln3g90/hFNbqe/hdVYF53sDVsj8Jb+5LtoYTpiAlV6eOvy1XR0vPZUf8w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/collections@3.12.0':
    resolution: {integrity: sha512-MfR9hwCxe5oXv4qrLUnjidwM50U35EFmInUeFf8i9mskYwWlRYS0O1/9PZ0oF1M0cKambaRHKEy98jczgb9ycA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/collections@3.12.3':
    resolution: {integrity: sha512-QfSBME2QWDjUw/RmmUjrYl/j1iCYcYCIDsgZda1OeRtt63R11k0aqmmwrDRwCsA+Sv+D5QgkOp4KK+CokTzoVQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/combobox@3.10.1':
    resolution: {integrity: sha512-Rso+H+ZEDGFAhpKWbnRxRR/r7YNmYVtt+Rn0eNDNIUp3bYaxIBCdCySyAtALs4I8RZXZQ9zoUznP7YeVwG3cLg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/datepicker@3.11.0':
    resolution: {integrity: sha512-d9MJF34A0VrhL5y5S8mAISA8uwfNCQKmR2k4KoQJm3De1J8SQeNzSjLviAwh1faDow6FXGlA6tVbTrHyDcBgBg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/flags@3.0.3':
    resolution: {integrity: sha512-/ha7XFA0RZTQsbzSPwu3KkbNMgbvuM0GuMTYLTBWpgBrovBNTM+QqI/PfZTdHg8PwCYF4H5Y8gjdSpdulCvJFw==}

  '@react-stately/flags@3.1.1':
    resolution: {integrity: sha512-XPR5gi5LfrPdhxZzdIlJDz/B5cBf63l4q6/AzNqVWFKgd0QqY5LvWJftXkklaIUpKSJkIKQb8dphuZXDtkWNqg==}

  '@react-stately/form@3.0.5':
    resolution: {integrity: sha512-J3plwJ63HQz109OdmaTqTA8Qhvl3gcYYK7DtgKyNP6mc/Me2Q4tl2avkWoA+22NRuv5m+J8TpBk4AVHUEOwqeQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/form@3.1.0':
    resolution: {integrity: sha512-E2wxNQ0QaTyDHD0nJFtTSnEH9A3bpJurwxhS4vgcUmESHgjFEMLlC9irUSZKgvOgb42GAq+fHoWBsgKeTp9Big==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/form@3.1.3':
    resolution: {integrity: sha512-Jisgm0facSS3sAzHfSgshoCo3LxfO0wmQj98MOBCGXyVL+MSwx2ilb38eXIyBCzHJzJnPRTLaK/E4T49aph47A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/grid@3.11.1':
    resolution: {integrity: sha512-xMk2YsaIKkF8dInRLUFpUXBIqnYt88hehhq2nb65RFgsFFhngE/OkaFudSUzaYPc1KvHpW+oHqvseC+G1iDG2w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/grid@3.9.2':
    resolution: {integrity: sha512-2gK//sqAqg2Xaq6UITTFQwFUJnBRgcW+cKBVbFt+F8d152xB6UwwTS/K79E5PUkOotwqZgTEpkrSFs/aVxCLpw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/list@3.10.3':
    resolution: {integrity: sha512-Ul8el0tQy2Ucl3qMQ0fiqdJ874W1ZNjURVSgSxN+pGwVLNBVRjd6Fl7YwZFCXER2YOlzkwg+Zqozf/ZlS0EdXA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/list@3.10.8':
    resolution: {integrity: sha512-rHCiPLXd+Ry3ztR9DkLA5FPQeH4Zd4/oJAEDWJ77W3oBBOdiMp3ZdHDLP7KBRh17XGNLO/QruYoHWAQTPiMF4g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/list@3.11.1':
    resolution: {integrity: sha512-UCOpIvqBOjwLtk7zVTYWuKU1m1Oe61Q5lNar/GwHaV1nAiSQ8/yYlhr40NkBEs9X3plEfsV28UIpzOrYnu1tPg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/list@3.12.1':
    resolution: {integrity: sha512-N+YCInNZ2OpY0WUNvJWUTyFHtzE5yBtZ9DI4EHJDvm61+jmZ2s3HszOfa7j+7VOKq78VW3m5laqsQNWvMrLFrQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/menu@3.9.0':
    resolution: {integrity: sha512-++sm0fzZeUs9GvtRbj5RwrP+KL9KPANp9f4SvtI3s+MP+Y/X3X7LNNePeeccGeyikB5fzMsuyvd82bRRW9IhDQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/menu@3.9.3':
    resolution: {integrity: sha512-9x1sTX3Xq2Q3mJUHV+YN9MR36qNzgn8eBSLa40eaFDaOOtoJ+V10m7OriUfpjey7WzLBpq00Sfda54/PbQHZ0g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/overlays@3.6.10':
    resolution: {integrity: sha512-XxZ2qScT5JPwGk9qiVJE4dtVh3AXTcYwGRA5RsHzC26oyVVsegPqY2PmNJGblAh6Q57VyodoVUyebE0Eo5CzRw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/overlays@3.6.12':
    resolution: {integrity: sha512-QinvZhwZgj8obUyPIcyURSCjTZlqZYRRCS60TF8jH8ZpT0tEAuDb3wvhhSXuYA3Xo9EHLwvLjEf3tQKKdAQArw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/overlays@3.6.15':
    resolution: {integrity: sha512-LBaGpXuI+SSd5HSGzyGJA0Gy09V2tl2G/r0lllTYqwt0RDZR6p7IrhdGVXZm6vI0oWEnih7yLC32krkVQrffgQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/overlays@3.6.5':
    resolution: {integrity: sha512-U4rCFj6TPJPXLUvYXAcvh+yP/CO2W+7f0IuqP7ZZGE+Osk9qFkT+zRK5/6ayhBDFpmueNfjIEAzT9gYPQwNHFw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/overlays@3.6.7':
    resolution: {integrity: sha512-6zp8v/iNUm6YQap0loaFx6PlvN8C0DgWHNlrlzMtMmNuvjhjR0wYXVaTfNoUZBWj25tlDM81ukXOjpRXg9rLrw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/radio@3.10.4':
    resolution: {integrity: sha512-kCIc7tAl4L7Hu4Wt9l2jaa+MzYmAJm0qmC8G8yPMbExpWbLRu6J8Un80GZu+JxvzgDlqDyrVvyv9zFifwH/NkQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/radio@3.10.9':
    resolution: {integrity: sha512-kUQ7VdqFke8SDRCatw2jW3rgzMWbvw+n2imN2THETynI47NmNLzNP11dlGO2OllRtTrsLhmBNlYHa3W62pFpAw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/select@3.6.12':
    resolution: {integrity: sha512-5o/NAaENO/Gxs1yui5BHLItxLnDPSQJ5HDKycuD0/gGC17BboAGEY/F9masiQ5qwRPe3JEc0QfvMRq3yZVNXog==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/selection@3.16.2':
    resolution: {integrity: sha512-C4eSKw7BIZHJLPzwqGqCnsyFHiUIEyryVQZTJDt6d0wYBOHU6k1pW+Q4VhrZuzSv+IMiI2RkiXeJKc55f0ZXrg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/selection@3.20.1':
    resolution: {integrity: sha512-K9MP6Rfg2yvFoY2Cr+ykA7bP4EBXlGaq5Dqfa1krvcXlEgMbQka5muLHdNXqjzGgcwPmS1dx1NECD15q63NtOw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/slider@3.6.0':
    resolution: {integrity: sha512-w5vJxVh267pmD1X+Ppd9S3ZzV1hcg0cV8q5P4Egr160b9WMcWlUspZPtsthwUlN7qQe/C8y5IAhtde4s29eNag==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/table@3.11.8':
    resolution: {integrity: sha512-EdyRW3lT1/kAVDp5FkEIi1BQ7tvmD2YgniGdLuW/l9LADo0T+oxZqruv60qpUS6sQap+59Riaxl91ClDxrJnpg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/table@3.13.0':
    resolution: {integrity: sha512-mRbNYrwQIE7xzVs09Lk3kPteEVFVyOc20vA8ph6EP54PiUf/RllJpxZe/WUYLf4eom9lUkRYej5sffuUBpxjCA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/tabs@3.7.0':
    resolution: {integrity: sha512-ox4hTkfZCoR4Oyr3Op3rBlWNq2Wxie04vhEYpTZQ2hobR3l4fYaOkd7CPClILktJ3TC104j8wcb0knWxIBRx9w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/toggle@3.7.4':
    resolution: {integrity: sha512-CoYFe9WrhLkDP4HGDpJYQKwfiYCRBAeoBQHv+JWl5eyK61S8xSwoHsveYuEZ3bowx71zyCnNAqWRrmNOxJ4CKA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/toggle@3.7.7':
    resolution: {integrity: sha512-AS+xB4+hHWa3wzYkbS6pwBkovPfIE02B9SnuYTe0stKcuejpWKo5L3QMptW0ftFYsW3ZPCXuneImfObEw2T01A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/toggle@3.8.0':
    resolution: {integrity: sha512-pyt/k/J8BwE/2g6LL6Z6sMSWRx9HEJB83Sm/MtovXnI66sxJ2EfQ1OaXB7Su5PEL9OMdoQF6Mb+N1RcW3zAoPw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/toggle@3.8.3':
    resolution: {integrity: sha512-4T2V3P1RK4zEFz4vJjUXUXyB0g4Slm6stE6Ry20fzDWjltuW42cD2lmrd7ccTO/CXFmHLECcXQLD4GEbOj0epA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/tooltip@3.4.9':
    resolution: {integrity: sha512-P7CDJsdoKarz32qFwf3VNS01lyC+63gXpDZG31pUu+EO5BeQd4WKN/AH1Beuswpr4GWzxzFc1aXQgERFGVzraA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/tooltip@3.5.0':
    resolution: {integrity: sha512-+xzPNztJDd2XJD0X3DgWKlrgOhMqZpSzsIssXeJgO7uCnP8/Z513ESaipJhJCFC8fxj5caO/DK4Uu8hEtlB8cQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/tree@3.8.6':
    resolution: {integrity: sha512-lblUaxf1uAuIz5jm6PYtcJ+rXNNVkqyFWTIMx6g6gW/mYvm8GNx1G/0MLZE7E6CuDGaO9dkLSY2bB1uqyKHidA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/tree@3.8.9':
    resolution: {integrity: sha512-j/LLI9UvbqcfOdl2v9m3gET3etUxoQzv3XdryNAbSkg0jTx8/13Fgi/Xp98bUcNLfynfeGW5P/fieU71sMkGog==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/utils@3.10.1':
    resolution: {integrity: sha512-VS/EHRyicef25zDZcM/ClpzYMC5i2YGN6uegOeQawmgfGjb02yaCX0F0zR69Pod9m2Hr3wunTbtpgVXvYbZItg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/utils@3.10.3':
    resolution: {integrity: sha512-moClv7MlVSHpbYtQIkm0Cx+on8Pgt1XqtPx6fy9rQFb2DNc9u1G3AUVnqA17buOkH1vLxAtX4MedlxMWyRCYYA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-stately/utils@3.10.5':
    resolution: {integrity: sha512-iMQSGcpaecghDIh3mZEpZfoFH3ExBwTtuBEcvZ2XnGzCgQjeYXcMdIUwAfVQLXFTdHUHGF6Gu6/dFrYsCzySBQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/utils@3.10.6':
    resolution: {integrity: sha512-O76ip4InfTTzAJrg8OaZxKU4vvjMDOpfA/PGNOytiXwBbkct2ZeZwaimJ8Bt9W1bj5VsZ81/o/tW4BacbdDOMA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-stately/utils@3.9.1':
    resolution: {integrity: sha512-yzw75GE0iUWiyps02BOAPTrybcsMIxEJlzXqtvllAb01O9uX5n0i3X+u2eCpj2UoDF4zS08Ps0jPgWxg8xEYtA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/virtualizer@3.7.1':
    resolution: {integrity: sha512-voHgE6EQ+oZaLv6u2umKxakvIKNkCQuUihqKACTjdslp7SJh4Mvs3oLBI0hf0JOh+rCcFIKDvQtFwy1fXFRYBA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-stately/virtualizer@4.2.0':
    resolution: {integrity: sha512-aTMpa9AQoz/xLqn8AI1BR/caUUY7/OUo9GbuF434w2u5eGCL7+SAn3Fmq7WSCwqYyDsO+jEIERek4JTX7pEW0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/accordion@3.0.0-alpha.25':
    resolution: {integrity: sha512-nPTRrMA5jS4QcwQ0H8J9Tzzw7+yq+KbwsPNA1ukVIfOGIB45by/1ke/eiZAXGqXxkElxi2fQuaXuWm79BWZ8zg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/breadcrumbs@3.7.9':
    resolution: {integrity: sha512-eARYJo8J+VfNV8vP4uw3L2Qliba9wLV2bx9YQCYf5Lc/OE5B/y4gaTLz+Y2P3Rtn6gBPLXY447zCs5i7gf+ICg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/button@3.10.1':
    resolution: {integrity: sha512-XTtap8o04+4QjPNAshFWOOAusUTxQlBjU2ai0BTVLShQEjHhRVDBIWsI2B2FKJ4KXT6AZ25llaxhNrreWGonmA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/button@3.12.0':
    resolution: {integrity: sha512-YrASNa+RqGQpzJcxNAahzNuTYVID1OE6HCorrEOXIyGS3EGogHsQmFs9OyThXnGHq6q4rLlA806/jWbP9uZdxA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/button@3.9.4':
    resolution: {integrity: sha512-raeQBJUxBp0axNF74TXB8/H50GY8Q3eV6cEKMbZFP1+Dzr09Ngv0tJBeW0ewAxAguNH5DRoMUAUGIXtSXskVdA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/button@3.9.6':
    resolution: {integrity: sha512-8lA+D5JLbNyQikf8M/cPP2cji91aVTcqjrGpDqI7sQnaLFikM8eFR6l1ZWGtZS5MCcbfooko77ha35SYplSQvw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/calendar@3.5.0':
    resolution: {integrity: sha512-O3IRE7AGwAWYnvJIJ80cOy7WwoJ0m8GtX/qSmvXQAjC4qx00n+b5aFNBYAQtcyc3RM5QpW6obs9BfwGetFiI8w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/calendar@3.7.0':
    resolution: {integrity: sha512-RiEfX2ZTcvfRktQc5obOJtNTgW+UwjNOUW5yf9CLCNOSM07e0w5jtC1ewsOZZbcctMrMCljjL8niGWiBv1wQ1Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/checkbox@3.8.1':
    resolution: {integrity: sha512-5/oVByPw4MbR/8QSdHCaalmyWC71H/QGgd4aduTJSaNi825o+v/hsN2/CH7Fq9atkLKsC8fvKD00Bj2VGaKriQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/checkbox@3.8.3':
    resolution: {integrity: sha512-f4c1mnLEt0iS1NMkyZXgT3q3AgcxzDk7w6MSONOKydcnh0xG5L2oefY14DhVDLkAuQS7jThlUFwiAs+MxiO3MA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/checkbox@3.9.0':
    resolution: {integrity: sha512-9hbHx0Oo2Hp5a8nV8Q75LQR0DHtvOIJbFaeqESSopqmV9EZoYjtY/h0NS7cZetgahQgnqYWQi44XGooMDCsmxA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/checkbox@3.9.3':
    resolution: {integrity: sha512-h6wmK7CraKHKE6L13Ut+CtnjRktbMRhkCSorv7eg82M6p4PDhZ7mfDSh13IlGR4sryT8Ka+aOjOU+EvMrKiduA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/combobox@3.13.1':
    resolution: {integrity: sha512-7xr+HknfhReN4QPqKff5tbKTe2kGZvH+DGzPYskAtb51FAAiZsKo+WvnNAvLwg3kRoC9Rkn4TAiVBp/HgymRDw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/datepicker@3.9.0':
    resolution: {integrity: sha512-dbKL5Qsm2MQwOTtVQdOcKrrphcXAqDD80WLlSQrBLg+waDuuQ7H+TrvOT0thLKloNBlFUGnZZfXGRHINpih/0g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/dialog@3.5.12':
    resolution: {integrity: sha512-JmpQbSpXltqEyYfEwoqDolABIiojeExkqolHNdQlayIsfFuSxZxNwXZPOpz58Ri/iwv21JP7K3QF0Gb2Ohxl9w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/dialog@3.5.17':
    resolution: {integrity: sha512-rKe2WrT272xuCH13euegBGjJAORYXJpHsX2hlu/f02TmMG4nSLss9vKBnY2N7k7nci65k5wDTW6lcsvQ4Co5zQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/form@3.7.8':
    resolution: {integrity: sha512-0wOS97/X0ijTVuIqik1lHYTZnk13QkvMTKvIEhM7c6YMU3vPiirBwLbT2kJiAdwLiymwcCkrBdDF1NTRG6kPFA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/grid@3.2.10':
    resolution: {integrity: sha512-Z5cG0ITwqjUE4kWyU5/7VqiPl4wqMJ7kG/ZP7poAnLmwRsR8Ai0ceVn+qzp5nTA19cgURi8t3LsXn3Ar1FBoog==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/grid@3.2.6':
    resolution: {integrity: sha512-XfHenL2jEBUYrhKiPdeM24mbLRXUn79wVzzMhrNYh24nBwhsPPpxF+gjFddT3Cy8dt6tRInfT6pMEu9nsXwaHw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/grid@3.2.8':
    resolution: {integrity: sha512-6PJrpukwMqlv3IhJSDkJuVbhHM8Oe6hd2supWqd9adMXrlSP7QHt9a8SgFcFblCCTx8JzUaA0PvY5sTudcEtOQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/grid@3.3.1':
    resolution: {integrity: sha512-bPDckheJiHSIzSeSkLqrO6rXRLWvciFJr9rpCjq/+wBj6HsLh2iMpkB/SqmRHTGpPlJvlu0b7AlxK1FYE0QSKA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/link@3.5.5':
    resolution: {integrity: sha512-G6P5WagHDR87npN7sEuC5IIgL1GsoY4WFWKO4734i2CXRYx24G9P0Su3AX4GA3qpspz8sK1AWkaCzBMmvnunfw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/link@3.5.7':
    resolution: {integrity: sha512-2WyaVmm1qr9UrSG3Dq6iz+2ziuVp+DH8CsYZ9CA6aNNb6U18Hxju3LTPb4a5gM0eC7W0mQGNBmrgGlAdDZEJOw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/link@3.5.9':
    resolution: {integrity: sha512-JcKDiDMqrq/5Vpn+BdWQEuXit4KN4HR/EgIi3yKnNbYkLzxBoeQZpQgvTaC7NEQeZnSqkyXQo3/vMUeX/ZNIKw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/link@3.6.0':
    resolution: {integrity: sha512-BQ5Tktb+fUxvtqksAJZuP8Z/bpmnQ/Y/zgwxfU0OKmIWkKMUsXY+e0GBVxwFxeh39D77stpVxRsTl7NQrjgtSw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/listbox@3.5.1':
    resolution: {integrity: sha512-n5bOgD9lgfK1qaLtag9WPnu151SwXBCNn/OgGY/Br9mWRl+nPUEYtFcPX+2VCld7uThf54kwrTmzlFnaraIlcw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/listbox@3.6.0':
    resolution: {integrity: sha512-+1ugDKTxson/WNOQZO4BfrnQ6cGDt+72mEytXMsSsd4aEC+x3RyUv6NKwdOl4n602cOreo0MHtap1X2BOACVoQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/menu@3.10.0':
    resolution: {integrity: sha512-DKMqEmUmarVCK0jblNkSlzSH53AAsxWCX9RaKZeP9EnRs2/l1oZRuiQVHlOQRgYwEigAXa2TrwcX4nnxZ+U36Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/menu@3.9.13':
    resolution: {integrity: sha512-7SuX6E2tDsqQ+HQdSvIda1ji/+ujmR86dtS9CUu5yWX91P25ufRjZ72EvLRqClWNQsj1Xl4+2zBDLWlceznAjw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/menu@3.9.7':
    resolution: {integrity: sha512-K6KhloJVoGsqwkdeez72fkNI9dfrmLI/sNrB4XuOKo2crDQ/eyZYWyJmzz8giz/tHME9w774k487rVoefoFh5w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/overlays@3.8.11':
    resolution: {integrity: sha512-aw7T0rwVI3EuyG5AOaEIk8j7dZJQ9m34XAztXJVZ/W2+4pDDkLDbJ/EAPnuo2xGYRGhowuNDn4tDju01eHYi+w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/overlays@3.8.14':
    resolution: {integrity: sha512-XJS67KHYhdMvPNHXNGdmc85gE+29QT5TwC58V4kxxHVtQh9fYzEEPzIV8K84XWSz04rRGe3fjDgRNbcqBektWQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/overlays@3.8.5':
    resolution: {integrity: sha512-4D7EEBQigD/m8hE68Ys8eloyyZFHHduqykSIgINJ0edmo0jygRbWlTwuhWFR9USgSP4dK54duN0Mvq0m4HEVEw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/overlays@3.8.7':
    resolution: {integrity: sha512-zCOYvI4at2DkhVpviIClJ7bRrLXYhSg3Z3v9xymuPH3mkiuuP/dm8mUCtkyY4UhVeUTHmrQh1bzaOP00A+SSQA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/overlays@3.8.9':
    resolution: {integrity: sha512-9ni9upQgXPnR+K9cWmbYWvm3ll9gH8P/XsEZprqIV5zNLMF334jADK48h4jafb1X9RFnj0WbHo6BqcSObzjTig==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/progress@3.5.8':
    resolution: {integrity: sha512-PR0rN5mWevfblR/zs30NdZr+82Gka/ba7UHmYOW9/lkKlWeD7PHgl1iacpd/3zl/jUF22evAQbBHmk1mS6Mpqw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/radio@3.8.1':
    resolution: {integrity: sha512-bK0gio/qj1+0Ldu/3k/s9BaOZvnnRgvFtL3u5ky479+aLG5qf1CmYed3SKz8ErZ70JkpuCSrSwSCFf0t1IHovw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/radio@3.8.5':
    resolution: {integrity: sha512-gSImTPid6rsbJmwCkTliBIU/npYgJHOFaI3PNJo7Y0QTAnFelCtYeFtBiWrFodSArSv7ASqpLLUEj9hZu/rxIg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/select@3.9.11':
    resolution: {integrity: sha512-uEpQCgDlrq/5fW05FgNEsqsqpvZVKfHQO9Mp7OTqGtm4UBNAbcQ6hOV7MJwQCS25Lu2luzOYdgqDUN8eAATJVQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/select@3.9.8':
    resolution: {integrity: sha512-RGsYj2oFjXpLnfcvWMBQnkcDuKkwT43xwYWZGI214/gp/B64tJiIUgTM5wFTRAeGDX23EePkhCQF+9ctnqFd6g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/shared@3.22.1':
    resolution: {integrity: sha512-PCpa+Vo6BKnRMuOEzy5zAZ3/H5tnQg1e80khMhK2xys0j6ZqzkgQC+fHMNZ7VDFNLqqNMj/o0eVeSBDh2POjkw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/shared@3.23.1':
    resolution: {integrity: sha512-5d+3HbFDxGZjhbMBeFHRQhexMFt4pUce3okyRtUVKbbedQFUrtXSBg9VszgF2RTeQDKDkMCIQDtz5ccP/Lk1gw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/shared@3.24.1':
    resolution: {integrity: sha512-AUQeGYEm/zDTN6zLzdXolDxz3Jk5dDL7f506F07U8tBwxNNI3WRdhU84G0/AaFikOZzDXhOZDr3MhQMzyE7Ydw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/shared@3.26.0':
    resolution: {integrity: sha512-6FuPqvhmjjlpEDLTiYx29IJCbCNWPlsyO+ZUmCUXzhUv2ttShOXfw8CmeHWHftT/b2KweAWuzqSlfeXPR76jpw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/shared@3.29.0':
    resolution: {integrity: sha512-IDQYu/AHgZimObzCFdNl1LpZvQW/xcfLt3v20sorl5qRucDVj4S9os98sVTZ4IRIBjmS+MkjqpR5E70xan7ooA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/slider@3.7.10':
    resolution: {integrity: sha512-Yb8wbpu2gS7AwvJUuz0IdZBRi6eIBZq32BSss4UHX0StA8dtR1/K4JeTsArxwiA3P0BA6t0gbR6wzxCvVA9fRw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/switch@3.5.10':
    resolution: {integrity: sha512-YyNhx4CvuJ0Rvv7yMuQaqQuOIeg+NwLV00NHHJ+K0xEANSLcICLOLPNMOqRIqLSQDz5vDI705UKk8gVcxqPX5g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/switch@3.5.5':
    resolution: {integrity: sha512-SZx1Bd+COhAOs/RTifbZG+uq/llwba7VAKx7XBeX4LeIz1dtguy5bigOBgFTMQi4qsIVCpybSWEEl+daj4XFPw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/table@3.10.3':
    resolution: {integrity: sha512-Ac+W+m/zgRzlTU8Z2GEg26HkuJFswF9S6w26r+R3MHwr8z2duGPvv37XRtE1yf3dbpRBgHEAO141xqS2TqGwNg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/table@3.9.5':
    resolution: {integrity: sha512-fgM2j9F/UR4Anmd28CueghCgBwOZoCVyN8fjaIFPd2MN4gCwUUfANwxLav65gZk4BpwUXGoQdsW+X50L3555mg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/tabs@3.3.11':
    resolution: {integrity: sha512-BjF2TqBhZaIcC4lc82R5pDJd1F7kstj1K0Nokhz99AGYn8C0ITdp6lR+DPVY9JZRxKgP9R2EKfWGI90Lo7NQdA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/textfield@3.10.0':
    resolution: {integrity: sha512-ShU3d6kLJGQjPXccVFjM3KOXdj3uyhYROqH9YgSIEVxgA9W6LRflvk/IVBamD9pJYTPbwmVzuP0wQkTDupfZ1w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/textfield@3.12.1':
    resolution: {integrity: sha512-6YTAMCKjEGuXg0A4bZA77j5QJ1a6yFviMUWsCIL6Dxq5K3TklzVsbAduSbHomPPuvkNTBSW4+TUJrVSnoTjMNA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/textfield@3.9.3':
    resolution: {integrity: sha512-DoAY6cYOL0pJhgNGI1Rosni7g72GAt4OVr2ltEx2S9ARmFZ0DBvdhA9lL2nywcnKMf27PEJcKMXzXc10qaHsJw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@react-types/textfield@3.9.6':
    resolution: {integrity: sha512-0uPqjJh4lYp1aL1HL9IlV8Cgp8eT0PcsNfdoCktfkLytvvBPmox2Pfm57W/d0xTtzZu2CjxhYNTob+JtGAOeXA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0

  '@react-types/tooltip@3.4.13':
    resolution: {integrity: sha512-KPekFC17RTT8kZlk7ZYubueZnfsGTDOpLw7itzolKOXGddTXsrJGBzSB4Bb060PBVllaDO0MOrhPap8OmrIl1Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-types/tooltip@3.4.9':
    resolution: {integrity: sha512-wZ+uF1+Zc43qG+cOJzioBmLUNjRa7ApdcT0LI1VvaYvH5GdfjzUJOorLX9V/vAci0XMJ50UZ+qsh79aUlw2yqg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0

  '@rushstack/eslint-patch@1.10.4':
    resolution: {integrity: sha512-WJgX9nzTqknM393q1QJDJmoW28kUfEnybeTfVNcNAPnIx210RXm2DiXiHzfNPJNIUUb1tJnz/l4QGtJ30PgWmA==}

  '@swc/counter@0.1.3':
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}

  '@swc/helpers@0.5.12':
    resolution: {integrity: sha512-KMZNXiGibsW9kvZAO1Pam2JPTDBm+KSHMMHWdsyI/1DbIZjT2A6Gy3hblVXUMEDvUAKq+e0vL0X0o54owWji7g==}

  '@swc/helpers@0.5.5':
    resolution: {integrity: sha512-KGYxvIOXcceOAbEk4bi/dVLEK9z8sZ0uBB3Il5b1rhfClSpcX0yfRO0KmTkqR2cnQDymwLB+25ZyMzICg/cm/A==}

  '@tanstack/react-virtual@3.10.6':
    resolution: {integrity: sha512-xaSy6uUxB92O8mngHZ6CvbhGuqxQ5lIZWCBy+FjhrbHmOwc6BnOnKkYm2FsB1/BpKw/+FVctlMbEtI+F6I1aJg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0

  '@tanstack/react-virtual@3.11.2':
    resolution: {integrity: sha512-OuFzMXPF4+xZgx8UzJha0AieuMihhhaWG0tCqpp6tDzlFwOmNBPYMuLOtMJ1Tr4pXLHmgjcWhG6RlknY2oNTdQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@tanstack/virtual-core@3.10.6':
    resolution: {integrity: sha512-1giLc4dzgEKLMx5pgKjL6HlG5fjZMgCjzlKAlpr7yoUtetVPELgER1NtephAI910nMwfPTHNyWKSFmJdHkz2Cw==}

  '@tanstack/virtual-core@3.11.2':
    resolution: {integrity: sha512-vTtpNt7mKCiZ1pwU9hfKPhpdVO2sVzFQsxoVBGtOSHxlrRRzYr8iQ2TlwbAcRYCcEiZ9ECAM8kBzH0v2+VzfKw==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/estree-jsx@1.0.5':
    resolution: {integrity: sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==}

  '@types/estree@1.0.6':
    resolution: {integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==}

  '@types/hast@3.0.4':
    resolution: {integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==}

  '@types/js-cookie@2.2.7':
    resolution: {integrity: sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==}

  '@types/json5@0.0.29':
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}

  '@types/lodash.debounce@4.0.9':
    resolution: {integrity: sha512-Ma5JcgTREwpLRwMM+XwBR7DaWe96nC38uCBDFKZWbNKD+osjVzdpnUSwBcqCptrp16sSOLBAUb50Car5I0TCsQ==}

  '@types/lodash@4.17.7':
    resolution: {integrity: sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA==}

  '@types/mdast@4.0.4':
    resolution: {integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==}

  '@types/ms@0.7.34':
    resolution: {integrity: sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g==}

  '@types/node@20.16.2':
    resolution: {integrity: sha512-91s/n4qUPV/wg8eE9KHYW1kouTfDk2FPGjXbBMfRWP/2vg1rCXNQL1OCabwGs0XSdukuK+MwCDXE30QpSeMUhQ==}

  '@types/prop-types@15.7.12':
    resolution: {integrity: sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==}

  '@types/react-dom@18.3.0':
    resolution: {integrity: sha512-EhwApuTmMBmXuFOikhQLIBUn6uFg81SwLMOAUgodJF14SOBOCMdU04gDoYi0WOJJHD144TL32z4yDqCW3dnkQg==}

  '@types/react@18.3.5':
    resolution: {integrity: sha512-WeqMfGJLGuLCqHGYRGHxnKrXcTitc6L/nBUWfWPcTarG3t9PsquqUMuVeXZeca+mglY4Vo5GZjCi0A3Or2lnxA==}

  '@types/unist@2.0.11':
    resolution: {integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==}

  '@types/unist@3.0.3':
    resolution: {integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==}

  '@typescript-eslint/parser@7.2.0':
    resolution: {integrity: sha512-5FKsVcHTk6TafQKQbuIVkXq58Fnbkd2wDL4LB7AURN7RUOu1utVP+G8+6u3ZhEroW3DF6hyo3ZEXxgKgp4KeCg==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@7.2.0':
    resolution: {integrity: sha512-Qh976RbQM/fYtjx9hs4XkayYujB/aPwglw2choHmf3zBjB4qOywWSdt9+KLRdHubGcoSwBnXUH2sR3hkyaERRg==}
    engines: {node: ^16.0.0 || >=18.0.0}

  '@typescript-eslint/types@7.2.0':
    resolution: {integrity: sha512-XFtUHPI/abFhm4cbCDc5Ykc8npOKBSJePY3a3s+lwumt7XWJuzP5cZcfZ610MIPHjQjNsOLlYK8ASPaNG8UiyA==}
    engines: {node: ^16.0.0 || >=18.0.0}

  '@typescript-eslint/typescript-estree@7.2.0':
    resolution: {integrity: sha512-cyxS5WQQCoBwSakpMrvMXuMDEbhOo9bNHHrNcEWis6XHx6KF518tkF1wBvKIn/tpq5ZpUYK7Bdklu8qY0MsFIA==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/visitor-keys@7.2.0':
    resolution: {integrity: sha512-c6EIQRHhcpl6+tO8EMR+kjkkV+ugUNXOmeASA1rlzkd8EPIriavpWoiEz1HR/VLhbVIdhqnV6E7JZm00cBDx2A==}
    engines: {node: ^16.0.0 || >=18.0.0}

  '@ungap/structured-clone@1.2.0':
    resolution: {integrity: sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==}

  '@xobotyi/scrollbar-width@1.9.5':
    resolution: {integrity: sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.12.1:
    resolution: {integrity: sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  aria-query@5.1.3:
    resolution: {integrity: sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==}

  array-buffer-byte-length@1.0.1:
    resolution: {integrity: sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==}
    engines: {node: '>= 0.4'}

  array-includes@3.1.8:
    resolution: {integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==}
    engines: {node: '>= 0.4'}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==}
    engines: {node: '>= 0.4'}

  array.prototype.findlastindex@1.2.5:
    resolution: {integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.2:
    resolution: {integrity: sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.2:
    resolution: {integrity: sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.3:
    resolution: {integrity: sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==}
    engines: {node: '>= 0.4'}

  ast-types-flow@0.0.8:
    resolution: {integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==}

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}

  axe-core@4.10.0:
    resolution: {integrity: sha512-Mr2ZakwQ7XUAjp7pAwQWRhhK8mQQ6JAaNWSjmjxil0R8BPioMtQsTLOolGYkji1rcL++3dCqZA3zWqpT+9Ew6g==}
    engines: {node: '>=4'}

  axobject-query@3.1.1:
    resolution: {integrity: sha512-goKlv8DZrK9hUh975fnHzhNIO4jUnFCfv/dszV5VwUGDFjI6vQ2VwoyjYjYNEbBE8AH87TduWP5uyDR1D+Iteg==}

  bail@2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  bufferutil@4.0.8:
    resolution: {integrity: sha512-4T53u4PdgsXqKaIctwF8ifXlRTTmEPJ8iEPWFdGZvcf7sbwYo6FKFEX9eNNAnzFZ7EzJAQ3CJeOtCRA4rDp7Pw==}
    engines: {node: '>=6.14.2'}

  busboy@1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==}
    engines: {node: '>=10.16.0'}

  call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  caniuse-lite@1.0.30001655:
    resolution: {integrity: sha512-jRGVy3iSGO5Uutn2owlb5gR6qsGngTw9ZTb4ali9f3glshcNmJ2noam4Mo9zia5P9Dk3jNNydy7vQjuE5dQmfg==}

  ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  character-entities-html4@2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}

  character-entities-legacy@3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}

  character-entities@2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}

  character-reference-invalid@2.0.1:
    resolution: {integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  client-only@0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}

  clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}

  color2k@2.0.3:
    resolution: {integrity: sha512-zW190nQTIoXcGCaU08DvVNFTmQhUpnJfVuAKfWqUQkflXKpaDdpaYoM0iluLS9lgJNHyBF58KKA2FBEwkD7wog==}

  color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}

  comma-separated-tokens@2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}

  commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  compute-scroll-into-view@3.1.0:
    resolution: {integrity: sha512-rj8l8pD4bJ1nx+dAkMhV1xB5RuZEyVysfxJqB1pRchh1KVvwOv9b7CGB8ZfjTImVv2oF+sYMUkMZq6Na5Ftmbg==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  css-in-js-utils@3.1.0:
    resolution: {integrity: sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==}

  css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  d@1.0.2:
    resolution: {integrity: sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==}
    engines: {node: '>=0.12'}

  damerau-levenshtein@1.0.8:
    resolution: {integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==}

  data-view-buffer@1.0.1:
    resolution: {integrity: sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.1:
    resolution: {integrity: sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.0:
    resolution: {integrity: sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==}
    engines: {node: '>= 0.4'}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.6:
    resolution: {integrity: sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decode-named-character-reference@1.0.2:
    resolution: {integrity: sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==}

  deep-equal@2.2.3:
    resolution: {integrity: sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==}
    engines: {node: '>= 0.4'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}

  devlop@1.1.0:
    resolution: {integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==}

  didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  enhanced-resolve@5.17.1:
    resolution: {integrity: sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==}
    engines: {node: '>=10.13.0'}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}

  eruda@3.3.0:
    resolution: {integrity: sha512-WsAEUIa9SNgLqaROMqnSvNeq6bySpjiG2ZQJYYGZw6VHmJ3zBBPCYyZDz0Dmymm9azh8TalkY2pEX2C2PiHIjw==}

  es-abstract@1.23.3:
    resolution: {integrity: sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-get-iterator@1.1.3:
    resolution: {integrity: sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==}

  es-iterator-helpers@1.0.19:
    resolution: {integrity: sha512-zoMwbCcH5hwUkKJkT8kDIBZSz9I6mVG//+lDCinLCGov4+r7NIy0ld8o03M0cJxl2spVf6ESYVS6/gpIfq1FFw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.0.0:
    resolution: {integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.0.3:
    resolution: {integrity: sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.0.2:
    resolution: {integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==}

  es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}

  es5-ext@0.10.64:
    resolution: {integrity: sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==}
    engines: {node: '>=0.10'}

  es6-iterator@2.0.3:
    resolution: {integrity: sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==}

  es6-symbol@3.1.4:
    resolution: {integrity: sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==}
    engines: {node: '>=0.12'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-config-next@14.2.3:
    resolution: {integrity: sha512-ZkNztm3Q7hjqvB1rRlOX8P9E/cXRL9ajRcs8jufEtwMfTVYRqnmtnaSu57QqHyBlovMuiB8LEzfLBkh5RYV6Fg==}
    peerDependencies:
      eslint: ^7.23.0 || ^8.0.0
      typescript: '>=3.3.1'
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}

  eslint-import-resolver-typescript@3.6.3:
    resolution: {integrity: sha512-ud9aw4szY9cCT1EWWdGv1L1XR6hh2PaRWif0j2QjQ0pgTY/69iw+W0Z4qZv5wHahOl8isEr+k/JnyAqNQkLkIA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      eslint-plugin-import: '*'
      eslint-plugin-import-x: '*'
    peerDependenciesMeta:
      eslint-plugin-import:
        optional: true
      eslint-plugin-import-x:
        optional: true

  eslint-module-utils@2.8.2:
    resolution: {integrity: sha512-3XnC5fDyc8M4J2E8pt8pmSVRX2M+5yWMCfI/kDZwauQeFgzQOuhcRBFKjTeJagqgk4sFKxe1mvNVnaWwImx/Tg==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-import@2.29.1:
    resolution: {integrity: sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-jsx-a11y@6.9.0:
    resolution: {integrity: sha512-nOFOCaJG2pYqORjK19lqPqxMO/JpvdCZdPtNdxY3kvom3jTvkAbOvQvD8wuD0G8BYR0IGAGYDlzqWJOh/ybn2g==}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8

  eslint-plugin-react-hooks@4.6.2:
    resolution: {integrity: sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0

  eslint-plugin-react@7.35.0:
    resolution: {integrity: sha512-v501SSMOWv8gerHkk+IIQBkcGRGrO2nfybfj5pLxuJNFTPxxA3PSryhXTK+9pNbtkggheDdsC0E9Q8CuPk6JKA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@8.57.0:
    resolution: {integrity: sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true

  esniff@2.0.1:
    resolution: {integrity: sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==}
    engines: {node: '>=0.10'}

  espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-util-is-identifier-name@3.0.0:
    resolution: {integrity: sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  event-emitter@0.3.5:
    resolution: {integrity: sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==}

  ext@1.7.0:
    resolution: {integrity: sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-shallow-equal@1.0.0:
    resolution: {integrity: sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==}

  fastest-stable-stringify@2.0.2:
    resolution: {integrity: sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==}

  fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true

  flatted@3.3.1:
    resolution: {integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==}

  for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}

  foreground-child@3.3.0:
    resolution: {integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==}
    engines: {node: '>=14'}

  framer-motion@12.11.0:
    resolution: {integrity: sha512-BaBPmkhaC2l0n619Kt1nQaxSdUdyyz5V1Z7EKJ1CcraOTZitgVx0RTbL8lmg2XesaFi6o8MPBIhkWDIvzDpGaQ==}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function.prototype.name@1.1.6:
    resolution: {integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}

  get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}

  get-symbol-description@1.0.2:
    resolution: {integrity: sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==}
    engines: {node: '>= 0.4'}

  get-tsconfig@4.8.0:
    resolution: {integrity: sha512-Pgba6TExTZ0FJAn1qkJAjIeKoDJ3CsI2ChuLohJnZl/tTU8MVrq3b+2t5UOPfRa4RMsorClBjJALkJUMjG1PAw==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob@10.3.10:
    resolution: {integrity: sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hast-util-to-jsx-runtime@2.3.2:
    resolution: {integrity: sha512-1ngXYb+V9UT5h+PxNRa1O1FYguZK/XL+gkeqvp7EdHlB9oHUG0eYRo/vY5inBdcqo3RkPMC58/H94HvkbfGdyg==}

  hast-util-whitespace@3.0.0:
    resolution: {integrity: sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==}

  html-url-attributes@3.0.1:
    resolution: {integrity: sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==}

  hyphenate-style-name@1.1.0:
    resolution: {integrity: sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  inline-style-parser@0.2.4:
    resolution: {integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==}

  inline-style-prefixer@7.0.1:
    resolution: {integrity: sha512-lhYo5qNTQp3EvSSp3sRvXMbVQTLrvGV6DycRMJ5dm2BLMiJ30wpXKdDdgX+GmJZ5uQMucwRKHamXSst3Sj/Giw==}

  input-otp@1.4.1:
    resolution: {integrity: sha512-+yvpmKYKHi9jIGngxagY9oWiiblPB7+nEO75F2l2o4vs+6vpPZZmUl4tBNYuTCvQjhvEIbdNeJu70bhfYP2nbw==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc

  internal-slot@1.0.7:
    resolution: {integrity: sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==}
    engines: {node: '>= 0.4'}

  intl-messageformat@10.5.14:
    resolution: {integrity: sha512-IjC6sI0X7YRjjyVH9aUgdftcmZK7WXdHeil4KwbjDnRWjnVitKpAx3rr6t6di1joFp5188VqKcobOPA6mCLG/w==}

  invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}

  is-alphabetical@2.0.1:
    resolution: {integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==}

  is-alphanumerical@2.0.1:
    resolution: {integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==}

  is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.4:
    resolution: {integrity: sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==}
    engines: {node: '>= 0.4'}

  is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}

  is-async-function@2.0.0:
    resolution: {integrity: sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==}
    engines: {node: '>= 0.4'}

  is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}

  is-bun-module@1.1.0:
    resolution: {integrity: sha512-4mTAVPlrXpaN3jtF0lsnPCMGnq4+qZjVIKq0HCpfcqf8OC1SM5oATCIAPM5V5FN05qp2NNnFndphmdZS9CV3hA==}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-core-module@2.15.1:
    resolution: {integrity: sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.1:
    resolution: {integrity: sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==}
    engines: {node: '>= 0.4'}

  is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}

  is-decimal@2.0.1:
    resolution: {integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.0.2:
    resolution: {integrity: sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-function@1.0.10:
    resolution: {integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-hexadecimal@2.0.1:
    resolution: {integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==}

  is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}

  is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}

  is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.3:
    resolution: {integrity: sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==}
    engines: {node: '>= 0.4'}

  is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}

  is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.13:
    resolution: {integrity: sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==}
    engines: {node: '>= 0.4'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}

  is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}

  is-weakset@2.0.3:
    resolution: {integrity: sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  iterator.prototype@1.1.2:
    resolution: {integrity: sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==}

  jackspeak@2.3.6:
    resolution: {integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==}
    engines: {node: '>=14'}

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jiti@1.21.6:
    resolution: {integrity: sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==}
    hasBin: true

  js-audio-recorder@1.0.7:
    resolution: {integrity: sha512-JiDODCElVHGrFyjGYwYyNi7zCbKk9va9C77w+zCPMmi4C6ix7zsX2h3ddHugmo4dOTOTCym9++b/wVW9nC0IaA==}

  js-cookie@2.2.1:
    resolution: {integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  language-subtag-registry@0.3.23:
    resolution: {integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==}

  language-tags@1.0.9:
    resolution: {integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==}
    engines: {node: '>=0.10'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  lilconfig@3.1.2:
    resolution: {integrity: sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.foreach@4.5.0:
    resolution: {integrity: sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ==}

  lodash.get@4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==}

  lodash.kebabcase@4.1.1:
    resolution: {integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==}

  lodash.mapkeys@4.6.0:
    resolution: {integrity: sha512-0Al+hxpYvONWtg+ZqHpa/GaVzxuN3V7Xeo2p+bY06EaK/n+Y9R7nBePPN2o1LxmL0TWQSwP8LYZ008/hc9JzhA==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.omit@4.5.0:
    resolution: {integrity: sha512-XeqSp49hNGmlkj2EJlfrQFIzQ6lXdNro9sddtQzcJY8QaoC2GO0DT7xaIokHeyM+mIT0mPMlPvkYzg2xCuHdZg==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  longest-streak@3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  mdast-util-from-markdown@2.0.1:
    resolution: {integrity: sha512-aJEUyzZ6TzlsX2s5B4Of7lN7EQtAxvtradMMglCQDyaTFgse6CmtmdJ15ElnVRlCg1vpNyVtbem0PWzlNieZsA==}

  mdast-util-mdx-expression@2.0.1:
    resolution: {integrity: sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==}

  mdast-util-mdx-jsx@3.1.3:
    resolution: {integrity: sha512-bfOjvNt+1AcbPLTFMFWY149nJz0OjmewJs3LQQ5pIyVGxP4CdOqNVJL6kTaM5c68p8q82Xv3nCyFfUnuEcH3UQ==}

  mdast-util-mdxjs-esm@2.0.1:
    resolution: {integrity: sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==}

  mdast-util-phrasing@4.1.0:
    resolution: {integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==}

  mdast-util-to-hast@13.2.0:
    resolution: {integrity: sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==}

  mdast-util-to-markdown@2.1.0:
    resolution: {integrity: sha512-SR2VnIEdVNCJbP6y7kVTJgPLifdr8WEU440fQec7qHoHOUz/oJ2jmNRqdDQ3rbiStOXb2mCDGTuwsK5OPUgYlQ==}

  mdast-util-to-string@4.0.0:
    resolution: {integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==}

  mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromark-core-commonmark@2.0.1:
    resolution: {integrity: sha512-CUQyKr1e///ZODyD1U3xit6zXwy1a8q2a1S1HKtIlmgvurrEpaw/Y9y6KSIbF8P59cn/NjzHyO+Q2fAyYLQrAA==}

  micromark-factory-destination@2.0.0:
    resolution: {integrity: sha512-j9DGrQLm/Uhl2tCzcbLhy5kXsgkHUrjJHg4fFAeoMRwJmJerT9aw4FEhIbZStWN8A3qMwOp1uzHr4UL8AInxtA==}

  micromark-factory-label@2.0.0:
    resolution: {integrity: sha512-RR3i96ohZGde//4WSe/dJsxOX6vxIg9TimLAS3i4EhBAFx8Sm5SmqVfR8E87DPSR31nEAjZfbt91OMZWcNgdZw==}

  micromark-factory-space@2.0.0:
    resolution: {integrity: sha512-TKr+LIDX2pkBJXFLzpyPyljzYK3MtmllMUMODTQJIUfDGncESaqB90db9IAUcz4AZAJFdd8U9zOp9ty1458rxg==}

  micromark-factory-title@2.0.0:
    resolution: {integrity: sha512-jY8CSxmpWLOxS+t8W+FG3Xigc0RDQA9bKMY/EwILvsesiRniiVMejYTE4wumNc2f4UbAa4WsHqe3J1QS1sli+A==}

  micromark-factory-whitespace@2.0.0:
    resolution: {integrity: sha512-28kbwaBjc5yAI1XadbdPYHX/eDnqaUFVikLwrO7FDnKG7lpgxnvk/XGRhX/PN0mOZ+dBSZ+LgunHS+6tYQAzhA==}

  micromark-util-character@2.1.0:
    resolution: {integrity: sha512-KvOVV+X1yLBfs9dCBSopq/+G1PcgT3lAK07mC4BzXi5E7ahzMAF8oIupDDJ6mievI6F+lAATkbQQlQixJfT3aQ==}

  micromark-util-chunked@2.0.0:
    resolution: {integrity: sha512-anK8SWmNphkXdaKgz5hJvGa7l00qmcaUQoMYsBwDlSKFKjc6gjGXPDw3FNL3Nbwq5L8gE+RCbGqTw49FK5Qyvg==}

  micromark-util-classify-character@2.0.0:
    resolution: {integrity: sha512-S0ze2R9GH+fu41FA7pbSqNWObo/kzwf8rN/+IGlW/4tC6oACOs8B++bh+i9bVyNnwCcuksbFwsBme5OCKXCwIw==}

  micromark-util-combine-extensions@2.0.0:
    resolution: {integrity: sha512-vZZio48k7ON0fVS3CUgFatWHoKbbLTK/rT7pzpJ4Bjp5JjkZeasRfrS9wsBdDJK2cJLHMckXZdzPSSr1B8a4oQ==}

  micromark-util-decode-numeric-character-reference@2.0.1:
    resolution: {integrity: sha512-bmkNc7z8Wn6kgjZmVHOX3SowGmVdhYS7yBpMnuMnPzDq/6xwVA604DuOXMZTO1lvq01g+Adfa0pE2UKGlxL1XQ==}

  micromark-util-decode-string@2.0.0:
    resolution: {integrity: sha512-r4Sc6leeUTn3P6gk20aFMj2ntPwn6qpDZqWvYmAG6NgvFTIlj4WtrAudLi65qYoaGdXYViXYw2pkmn7QnIFasA==}

  micromark-util-encode@2.0.0:
    resolution: {integrity: sha512-pS+ROfCXAGLWCOc8egcBvT0kf27GoWMqtdarNfDcjb6YLuV5cM3ioG45Ys2qOVqeqSbjaKg72vU+Wby3eddPsA==}

  micromark-util-html-tag-name@2.0.0:
    resolution: {integrity: sha512-xNn4Pqkj2puRhKdKTm8t1YHC/BAjx6CEwRFXntTaRf/x16aqka6ouVoutm+QdkISTlT7e2zU7U4ZdlDLJd2Mcw==}

  micromark-util-normalize-identifier@2.0.0:
    resolution: {integrity: sha512-2xhYT0sfo85FMrUPtHcPo2rrp1lwbDEEzpx7jiH2xXJLqBuy4H0GgXk5ToU8IEwoROtXuL8ND0ttVa4rNqYK3w==}

  micromark-util-resolve-all@2.0.0:
    resolution: {integrity: sha512-6KU6qO7DZ7GJkaCgwBNtplXCvGkJToU86ybBAUdavvgsCiG8lSSvYxr9MhwmQ+udpzywHsl4RpGJsYWG1pDOcA==}

  micromark-util-sanitize-uri@2.0.0:
    resolution: {integrity: sha512-WhYv5UEcZrbAtlsnPuChHUAsu/iBPOVaEVsntLBIdpibO0ddy8OzavZz3iL2xVvBZOpolujSliP65Kq0/7KIYw==}

  micromark-util-subtokenize@2.0.1:
    resolution: {integrity: sha512-jZNtiFl/1aY73yS3UGQkutD0UbhTt68qnRpw2Pifmz5wV9h8gOVsN70v+Lq/f1rKaU/W8pxRe8y8Q9FX1AOe1Q==}

  micromark-util-symbol@2.0.0:
    resolution: {integrity: sha512-8JZt9ElZ5kyTnO94muPxIGS8oyElRJaiJO8EzV6ZSyGQ1Is8xwl4Q45qU5UOg+bGH4AikWziz0iN4sFLWs8PGw==}

  micromark-util-types@2.0.0:
    resolution: {integrity: sha512-oNh6S2WMHWRZrmutsRmDDfkzKtxF+bc2VxLC9dvtrDIRFln627VsFP6fLMgTryGDljgLPjkrzQSDcPrjPyDJ5w==}

  micromark@4.0.0:
    resolution: {integrity: sha512-o/sd0nMof8kYff+TqcDx3VSrgBTcZpSvYcAHIfHhv5VAuNmisCxjhx6YmxS8PFEpb9z5WKWKPdzf0jM23ro3RQ==}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  motion-dom@12.11.0:
    resolution: {integrity: sha512-CItkGYJenn5ZsbzTX0D9mE0UWdjdd9r535FrxEXhzR8Kwa9I2dLr1uhEJgQPWbgaIJ6i0sNFnf2T9NvVDWQVBw==}

  motion-utils@12.9.4:
    resolution: {integrity: sha512-BW3I65zeM76CMsfh3kHid9ansEJk9Qvl+K5cu4DVHKGsI52n76OJ4z2CUJUV+Mn3uEP9k1JJA3tClG0ggSrRcg==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  nano-css@5.6.2:
    resolution: {integrity: sha512-+6bHaC8dSDGALM1HJjOHVXpuastdu2xFoZlC77Jh4cg+33Zcgm+Gxd+1xsnpZK14eyHObSp82+ll5y3SX75liw==}
    peerDependencies:
      react: '*'
      react-dom: '*'

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  next-tick@1.1.0:
    resolution: {integrity: sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==}

  next@14.2.3:
    resolution: {integrity: sha512-dowFkFTR8v79NPJO4QsBUtxv0g9BrS/phluVpMAt2ku7H+cbcBJlopXjkWlwxrk/xGqMemr7JkGPGemPrLLX7A==}
    engines: {node: '>=18.17.0'}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      '@playwright/test': ^1.41.2
      react: ^18.2.0
      react-dom: ^18.2.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@playwright/test':
        optional: true
      sass:
        optional: true

  node-gyp-build@4.8.2:
    resolution: {integrity: sha512-IRUxE4BVsHWXkV/SFOut4qTlagw2aM8T5/vnTsmrHJvVoKueJHRc/JaFND7QDDc61kLYUJ6qlZM3sqTSyx2dTw==}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  object-inspect@1.13.2:
    resolution: {integrity: sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==}
    engines: {node: '>= 0.4'}

  object-is@1.1.6:
    resolution: {integrity: sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.5:
    resolution: {integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==}
    engines: {node: '>= 0.4'}

  object.entries@1.1.8:
    resolution: {integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}

  object.groupby@1.0.3:
    resolution: {integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==}
    engines: {node: '>= 0.4'}

  object.values@1.2.0:
    resolution: {integrity: sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==}
    engines: {node: '>= 0.4'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  or@0.2.0:
    resolution: {integrity: sha512-BHB8VZq2isxkyRaCBZ6CZCbQBzCT+gy8LPiqdbMH1+Fd6biFj3v8ebjeYzzL51PbsApsPYnGegGTO6KLQMxxDw==}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  package-json-from-dist@1.0.0:
    resolution: {integrity: sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-entities@4.0.1:
    resolution: {integrity: sha512-SWzvYcSJh4d/SGLIOQfZ/CoNv6BTlI6YEQ7Nj82oDVnRpwe/Z/F1EMx42x3JAOwGBlCjeCH0BRJQbQ/opHL17w==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  picocolors@1.0.1:
    resolution: {integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  possible-typed-array-names@1.0.0:
    resolution: {integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==}
    engines: {node: '>= 0.4'}

  postcss-import@15.1.0:
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution: {integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.4.41:
    resolution: {integrity: sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  property-information@6.5.0:
    resolution: {integrity: sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-markdown@9.0.1:
    resolution: {integrity: sha512-186Gw/vF1uRkydbsOIkcGXw7aHq0sZOCRFFjGrr7b9+nVZg4UfA4enXCaxm4fUzecU38sWfrNDitGhshuU7rdg==}
    peerDependencies:
      '@types/react': '>=18'
      react: '>=18'

  react-remove-scroll-bar@2.3.6:
    resolution: {integrity: sha512-DtSYaao4mBmX+HDo5YWYdBWQwYIQQshUV/dVxFxK+KM26Wjwp1gZ6rv6OC3oujI6Bfu6Xyg3TwK533AQutsn/g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.5.10:
    resolution: {integrity: sha512-m3zvBRANPBw3qxVVjEIPEQinkcwlFZ4qyomuWVpNJdv4c6MvHfXV0C3L9Jx5rr3HeBHKNRX+1jreB5QloDIJjA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-style-singleton@2.2.1:
    resolution: {integrity: sha512-ZWj0fHEMyWkHzKYUr2Bs/4zU6XLmq9HsgBURm7g5pAVfyn49DgUiNgY2d4lXRlYSiCif9YBGpQleewkcqddc7g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-textarea-autosize@8.5.3:
    resolution: {integrity: sha512-XT1024o2pqCuZSuBt9FwHlaDeNtVrtCXu0Rnz88t1jUGheCLa3PhjE1GH8Ctm2axEtvdCl5SUHYschyQ0L5QHQ==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  react-universal-interface@0.6.2:
    resolution: {integrity: sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==}
    peerDependencies:
      react: '*'
      tslib: '*'

  react-use-websocket@4.8.1:
    resolution: {integrity: sha512-FTXuG5O+LFozmu1BRfrzl7UIQngECvGJmL7BHsK4TYXuVt+mCizVA8lT0hGSIF0Z0TedF7bOo1nRzOUdginhDw==}
    peerDependencies:
      react: '>= 18.0.0'
      react-dom: '>= 18.0.0'

  react-use@17.5.1:
    resolution: {integrity: sha512-LG/uPEVRflLWMwi3j/sZqR00nF6JGqTTDblkXK2nzXsIvij06hXl1V/MZIlwj1OKIQUtlh1l9jK8gLsRyCQxMg==}
    peerDependencies:
      react: '*'
      react-dom: '*'

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  reflect.getprototypeof@1.0.6:
    resolution: {integrity: sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg==}
    engines: {node: '>= 0.4'}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regexp.prototype.flags@1.5.2:
    resolution: {integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==}
    engines: {node: '>= 0.4'}

  remark-parse@11.0.0:
    resolution: {integrity: sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==}

  remark-rehype@11.1.1:
    resolution: {integrity: sha512-g/osARvjkBXb6Wo0XvAeXQohVta8i84ACbenPpoSsxTOQH/Ae0/RGP4WZgnMH5pMLpsj4FG7OHmcIcXxpza8eQ==}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rtl-css-js@1.16.1:
    resolution: {integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safe-array-concat@1.1.2:
    resolution: {integrity: sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==}
    engines: {node: '>=0.4'}

  safe-regex-test@1.0.3:
    resolution: {integrity: sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==}
    engines: {node: '>= 0.4'}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  screenfull@5.2.0:
    resolution: {integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==}
    engines: {node: '>=0.10.0'}

  scroll-into-view-if-needed@3.0.10:
    resolution: {integrity: sha512-t44QCeDKAPf1mtQH3fYpWz8IM/DyvHLjs8wUvvwMYxk5moOqCzrMSxK6HQVD0QVmVjXFavoFIPRVrMuJPKAvtg==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  set-harmonic-interval@1.0.1:
    resolution: {integrity: sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==}
    engines: {node: '>=6.9'}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}

  source-map@0.5.6:
    resolution: {integrity: sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  space-separated-tokens@2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}

  stack-generator@2.0.10:
    resolution: {integrity: sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==}

  stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}

  stacktrace-gps@3.1.2:
    resolution: {integrity: sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==}

  stacktrace-js@2.0.2:
    resolution: {integrity: sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==}

  stop-iteration-iterator@1.0.0:
    resolution: {integrity: sha512-iCGQj+0l0HOdZ2AEeBADlsRC+vsnDsZsbdSiH1yNSjcfKM7fdpCMfqAL/dwF5BLiw/XhRft/Wax6zQbhq2BcjQ==}
    engines: {node: '>= 0.4'}

  streamsearch@1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==}
    engines: {node: '>=10.0.0'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string.prototype.includes@2.0.0:
    resolution: {integrity: sha512-E34CkBgyeqNDcrbU76cDjL5JLcVrtSdYq0MEh/B10r17pRP4ciHLwTgnuLV8Ay6cgEMLkcBkFCKyFZ43YldYzg==}

  string.prototype.matchall@4.0.11:
    resolution: {integrity: sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==}

  string.prototype.trim@1.2.9:
    resolution: {integrity: sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.8:
    resolution: {integrity: sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}

  stringify-entities@4.0.4:
    resolution: {integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  style-to-object@1.0.8:
    resolution: {integrity: sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==}

  styled-jsx@5.1.1:
    resolution: {integrity: sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true

  stylis@4.3.4:
    resolution: {integrity: sha512-osIBl6BGUmSfDkyH2mB7EFvCJntXDrLhKjHTRj/rK6xLH0yuPrHULDRQzKokSOD4VoorhtKpfcfW1GAntu8now==}

  sucrase@3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}

  tailwind-merge@1.14.0:
    resolution: {integrity: sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==}

  tailwind-merge@2.6.0:
    resolution: {integrity: sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==}

  tailwind-variants@0.1.20:
    resolution: {integrity: sha512-AMh7x313t/V+eTySKB0Dal08RHY7ggYK0MSn/ad8wKWOrDUIzyiWNayRUm2PIJ4VRkvRnfNuyRuKbLV3EN+ewQ==}
    engines: {node: '>=16.x', pnpm: '>=7.x'}
    peerDependencies:
      tailwindcss: '*'

  tailwindcss@3.4.10:
    resolution: {integrity: sha512-KWZkVPm7yJRhdu4SRSl9d4AK2wM3a50UsvgHZO7xY77NQr2V+fIrEuoDGQcbvswWvFGbS2f6e+jC/6WJm1Dl0w==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  throttle-debounce@3.0.1:
    resolution: {integrity: sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==}
    engines: {node: '>=10'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}

  trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}

  trough@2.2.0:
    resolution: {integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==}

  ts-api-utils@1.3.0:
    resolution: {integrity: sha512-UQMIo7pb8WRomKR1/+MFVLTroIvDVtMX3K6OUir8ynLyzB8Jeriont2bTAtmNPa1ekAgN7YPDyf6V+ygrdU+eQ==}
    engines: {node: '>=16'}
    peerDependencies:
      typescript: '>=4.2.0'

  ts-easing@0.2.0:
    resolution: {integrity: sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==}

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}

  tslib@2.7.0:
    resolution: {integrity: sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  type@2.7.3:
    resolution: {integrity: sha512-8j+1QmAbPvLZow5Qpi6NCaN8FB60p/6x8/vfNqOk/hC+HuvFZhL4+WfekuhQLiqFZXOgQdrs3B+XxEmCc6b3FQ==}

  typed-array-buffer@1.0.2:
    resolution: {integrity: sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.1:
    resolution: {integrity: sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.2:
    resolution: {integrity: sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.6:
    resolution: {integrity: sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==}
    engines: {node: '>= 0.4'}

  typedarray-to-buffer@3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}

  typescript@5.5.4:
    resolution: {integrity: sha512-Mtq29sKDAEYP7aljRgtPOpTvOfbwRWlS6dPRzwjdE+C0R4brX/GUyhHSecbHMFLNBLcJIPt9nl9yG5TZ1weH+Q==}
    engines: {node: '>=14.17'}
    hasBin: true

  unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}

  undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}

  unified@11.0.5:
    resolution: {integrity: sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==}

  unist-util-is@6.0.0:
    resolution: {integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==}

  unist-util-position@5.0.0:
    resolution: {integrity: sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==}

  unist-util-stringify-position@4.0.0:
    resolution: {integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==}

  unist-util-visit-parents@6.0.1:
    resolution: {integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==}

  unist-util-visit@5.0.0:
    resolution: {integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==}

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  use-callback-ref@1.3.2:
    resolution: {integrity: sha512-elOQwe6Q8gqZgDA8mrh44qRTQqpIHDcZ3hXTLjBe1i4ph8XpNJnO+aQf3NaG+lriLopI4HMx9VjQLfPQ6vhnoA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-composed-ref@1.3.0:
    resolution: {integrity: sha512-GLMG0Jc/jiKov/3Ulid1wbv3r54K9HlMW29IWcDFPEqFkSO2nS0MuefWgMJpeHQ9YJeXDL3ZUF+P3jdXlZX/cQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  use-isomorphic-layout-effect@1.1.2:
    resolution: {integrity: sha512-49L8yCO3iGT/ZF9QttjwLF/ZD9Iwto5LnH5LmEdk/6cFmXddqi2ulF0edxTwjj+7mqvpVVGQWvbXZdn32wRSHA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-latest@1.2.1:
    resolution: {integrity: sha512-xA+AVm/Wlg3e2P/JiItTziwS7FK92LWrDB0p+hgXloIMuVCeJJ8v6f0eeHyPZaJrM+usM1FkFfbNCrJGs8A/zw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.2:
    resolution: {integrity: sha512-epTbsLuzZ7lPClpz2TyryBfztm7m+28DlEv2ZCQ3MDr5ssiwyOwGH/e5F9CkfWjJ1t4clvI58yF822/GUkjjhw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.9.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sync-external-store@1.2.2:
    resolution: {integrity: sha512-PElTlVMwpblvbNqQ82d2n6RjStvdSoNe9FG28kNfz3WiXilJm4DdNkEzRhCZuIDwY8U08WVihhGR5iRqAwfDiw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  utf-8-validate@5.0.10:
    resolution: {integrity: sha512-Z6czzLq4u8fPOyx7TU6X3dvUZVvoJmxSQ+IcrlmagKhilxlhZgxPK6C5Jqbkw1IDUmFTM+cz9QDnnLTwDz/2gQ==}
    engines: {node: '>=6.14.2'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  vfile-message@4.0.2:
    resolution: {integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==}

  vfile@6.0.3:
    resolution: {integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==}

  websocket@1.0.35:
    resolution: {integrity: sha512-/REy6amwPZl44DDzvRCkaI1q1bIiQB0mEFQLUrhz3z2EK91cp3n72rAjUlrTP0zV22HJIUOVHQGPxhFRjxjt+Q==}
    engines: {node: '>=4.0.0'}

  whatwg-fetch@3.6.20:
    resolution: {integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==}

  which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}

  which-builtin-type@1.1.4:
    resolution: {integrity: sha512-bppkmBSsHFmIMSl8BO9TbsyzsvGjVoppt8xUiGzwiu/bhDCGxnpOKCxgqj6GuyHE0mINMDecBFPlOm2hzY084w==}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.15:
    resolution: {integrity: sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  yaeti@0.0.6:
    resolution: {integrity: sha512-MvQa//+KcZCUkBTIC9blM+CU9J2GzuTytsOUwf2lidtvkx/6gnEp1QvJv34t9vdjhFmha/mUiNDbN0D0mJWdug==}
    engines: {node: '>=0.10.32'}

  yaml@2.5.0:
    resolution: {integrity: sha512-2wWLbGbYDiSqqIKoPjar3MPgB94ErzCtrNE1FdqGuaO0pi2JGjmE8aW8TDZwzU7vuxcGRdL/4gPQwQ7hD5AMSw==}
    engines: {node: '>= 14'}
    hasBin: true

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  zustand@4.5.5:
    resolution: {integrity: sha512-+0PALYNJNgK6hldkgDq2vLrw5f6g/jCInz52n9RTpropGgeAf/ioFUCdtsjCqu4gNhW9D01rUQBROoRjdzyn2Q==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0.6'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true

  zwitch@2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}

snapshots:

  '@alloc/quick-lru@5.2.0': {}

  '@babel/runtime@7.25.6':
    dependencies:
      regenerator-runtime: 0.14.1

  '@eslint-community/eslint-utils@4.4.0(eslint@8.57.0)':
    dependencies:
      eslint: 8.57.0
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.11.0': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.6
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.0': {}

  '@floating-ui/core@1.6.7':
    dependencies:
      '@floating-ui/utils': 0.2.7

  '@floating-ui/dom@1.6.10':
    dependencies:
      '@floating-ui/core': 1.6.7
      '@floating-ui/utils': 0.2.7

  '@floating-ui/react-dom@2.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/dom': 1.6.10
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@floating-ui/react@0.26.23(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react-dom': 2.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@floating-ui/utils': 0.2.7
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tabbable: 6.2.0

  '@floating-ui/utils@0.2.7': {}

  '@formatjs/ecma402-abstract@2.0.0':
    dependencies:
      '@formatjs/intl-localematcher': 0.5.4
      tslib: 2.7.0

  '@formatjs/fast-memoize@2.2.0':
    dependencies:
      tslib: 2.7.0

  '@formatjs/icu-messageformat-parser@2.7.8':
    dependencies:
      '@formatjs/ecma402-abstract': 2.0.0
      '@formatjs/icu-skeleton-parser': 1.8.2
      tslib: 2.7.0

  '@formatjs/icu-skeleton-parser@1.8.2':
    dependencies:
      '@formatjs/ecma402-abstract': 2.0.0
      tslib: 2.7.0

  '@formatjs/intl-localematcher@0.5.4':
    dependencies:
      tslib: 2.7.0

  '@headlessui/react@2.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react': 0.26.23(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@tanstack/react-virtual': 3.10.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@heroicons/react@2.1.5(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@humanwhocodes/config-array@0.11.14':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.3.6
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@internationalized/date@3.5.5':
    dependencies:
      '@swc/helpers': 0.5.12

  '@internationalized/date@3.6.0':
    dependencies:
      '@swc/helpers': 0.5.12

  '@internationalized/date@3.8.0':
    dependencies:
      '@swc/helpers': 0.5.12

  '@internationalized/message@3.1.4':
    dependencies:
      '@swc/helpers': 0.5.12
      intl-messageformat: 10.5.14

  '@internationalized/message@3.1.7':
    dependencies:
      '@swc/helpers': 0.5.12
      intl-messageformat: 10.5.14

  '@internationalized/number@3.5.3':
    dependencies:
      '@swc/helpers': 0.5.12

  '@internationalized/number@3.6.1':
    dependencies:
      '@swc/helpers': 0.5.12

  '@internationalized/string@3.2.3':
    dependencies:
      '@swc/helpers': 0.5.12

  '@internationalized/string@3.2.6':
    dependencies:
      '@swc/helpers': 0.5.12

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@next/env@14.2.3': {}

  '@next/eslint-plugin-next@14.2.3':
    dependencies:
      glob: 10.3.10

  '@next/swc-darwin-arm64@14.2.3':
    optional: true

  '@next/swc-darwin-x64@14.2.3':
    optional: true

  '@next/swc-linux-arm64-gnu@14.2.3':
    optional: true

  '@next/swc-linux-arm64-musl@14.2.3':
    optional: true

  '@next/swc-linux-x64-gnu@14.2.3':
    optional: true

  '@next/swc-linux-x64-musl@14.2.3':
    optional: true

  '@next/swc-win32-arm64-msvc@14.2.3':
    optional: true

  '@next/swc-win32-ia32-msvc@14.2.3':
    optional: true

  '@next/swc-win32-x64-msvc@14.2.3':
    optional: true

  '@nextui-org/accordion@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/divider': 2.2.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-accordion': 2.2.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/button': 3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/tree': 3.8.6(react@18.3.1)
      '@react-types/accordion': 3.0.0-alpha.25(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/alert@2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - framer-motion

  '@nextui-org/aria-utils@2.0.20(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.12
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/collections': 3.10.5(react@18.3.1)
      '@react-stately/overlays': 3.6.5(react@18.3.1)
      '@react-types/overlays': 3.8.5(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'
      - framer-motion

  '@nextui-org/aria-utils@2.0.21(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.12
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/collections': 3.10.7(react@18.3.1)
      '@react-stately/overlays': 3.6.7(react@18.3.1)
      '@react-types/overlays': 3.8.7(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'
      - framer-motion

  '@nextui-org/aria-utils@2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/collections': 3.12.0(react@18.3.1)
      '@react-stately/overlays': 3.6.12(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'
      - framer-motion

  '@nextui-org/autocomplete@2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(@types/react@18.3.5)(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/input': 2.4.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/listbox': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/popover': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/scroll-shadow': 2.3.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/spinner': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/combobox': 3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/combobox': 3.10.1(react@18.3.1)
      '@react-types/combobox': 3.13.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@nextui-org/avatar@2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-image': 2.1.2(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/badge@2.2.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/breadcrumbs@2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/breadcrumbs': 3.5.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/breadcrumbs': 3.7.9(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/button@2.0.34(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/ripple': 2.0.30(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/spinner': 2.0.30(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.0.9(react@18.3.1)
      '@react-aria/button': 3.9.5(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-types/button': 3.9.4(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/button@2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/ripple': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/spinner': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@react-aria/button': 3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/button': 3.10.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/calendar@2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@react-aria/calendar': 3.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/calendar': 3.6.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/button': 3.10.1(react@18.3.1)
      '@react-types/calendar': 3.5.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@types/lodash.debounce': 4.0.9
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      scroll-into-view-if-needed: 3.0.10

  '@nextui-org/card@2.0.33(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/ripple': 2.0.32(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.0.10(react@18.3.1)
      '@react-aria/button': 3.9.5(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/card@2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/ripple': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@react-aria/button': 3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/checkbox@2.1.4(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-callback-ref': 2.0.6(react@18.3.1)
      '@nextui-org/use-safe-layout-effect': 2.0.6(react@18.3.1)
      '@react-aria/checkbox': 3.14.3(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.12(react@18.3.1)
      '@react-stately/checkbox': 3.6.5(react@18.3.1)
      '@react-stately/toggle': 3.7.4(react@18.3.1)
      '@react-types/checkbox': 3.8.1(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/checkbox@2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-callback-ref': 2.1.1(react@18.3.1)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/checkbox': 3.15.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/checkbox': 3.6.10(react@18.3.1)
      '@react-stately/toggle': 3.8.0(react@18.3.1)
      '@react-types/checkbox': 3.9.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/chip@2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/checkbox': 3.9.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/code@2.0.29(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system-rsc': 2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/code@2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system-rsc': 2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/date-input@2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/datepicker': 3.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/datepicker': 3.11.0(react@18.3.1)
      '@react-types/datepicker': 3.9.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/date-picker@2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/calendar': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/date-input': 2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/popover': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/datepicker': 3.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/datepicker': 3.11.0(react@18.3.1)
      '@react-stately/overlays': 3.6.12(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/datepicker': 3.9.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/divider@2.0.28(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.12
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system-rsc': 2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@react-types/shared': 3.22.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/divider@2.2.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system-rsc': 2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/dom-animation@2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))':
    dependencies:
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)

  '@nextui-org/drawer@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/modal': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - framer-motion

  '@nextui-org/dropdown@2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/menu': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/popover': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/menu': 3.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/menu': 3.9.0(react@18.3.1)
      '@react-types/menu': 3.9.13(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/form@2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/form': 3.1.0(react@18.3.1)
      '@react-types/form': 3.7.8(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/framer-utils@2.0.21(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-measure': 2.0.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/framer-utils@2.0.24(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system': 2.2.5(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-measure': 2.0.2(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/framer-utils@2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-measure': 2.1.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/image@2.2.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-image': 2.1.2(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/input-otp@2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/form': 3.0.11(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/form': 3.1.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/textfield': 3.10.0(react@18.3.1)
      input-otp: 1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/input@2.2.2(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-icons': 2.0.8(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-safe-layout-effect': 2.0.5(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/textfield': 3.14.5(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/utils': 3.10.1(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      '@react-types/textfield': 3.9.3(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-textarea-autosize: 8.5.3(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@nextui-org/input@2.4.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/textfield': 3.15.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@react-types/textfield': 3.10.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-textarea-autosize: 8.5.3(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@nextui-org/kbd@2.0.30(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system-rsc': 2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/kbd@2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system-rsc': 2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/link@2.0.32(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-icons': 2.0.8(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-link': 2.0.18(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/link': 3.7.1(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-types/link': 3.5.5(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/link@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-link': 2.2.5(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/link': 3.7.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/link': 3.5.9(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/listbox@2.1.21(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.0.20(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/divider': 2.0.28(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.0.13(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-is-mobile': 2.0.7(react@18.3.1)
      '@react-aria/focus': 3.16.2(react@18.3.1)
      '@react-aria/interactions': 3.21.1(react@18.3.1)
      '@react-aria/listbox': 3.11.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/list': 3.10.3(react@18.3.1)
      '@react-types/menu': 3.9.7(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - framer-motion

  '@nextui-org/listbox@2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/divider': 2.2.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-is-mobile': 2.2.2(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/listbox': 3.13.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/list': 3.11.1(react@18.3.1)
      '@react-types/menu': 3.9.13(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@tanstack/react-virtual': 3.11.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - framer-motion

  '@nextui-org/menu@2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/divider': 2.2.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-is-mobile': 2.2.2(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/menu': 3.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/menu': 3.9.0(react@18.3.1)
      '@react-stately/tree': 3.8.6(react@18.3.1)
      '@react-types/menu': 3.9.13(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - framer-motion

  '@nextui-org/modal@2.0.39(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/framer-utils': 2.0.24(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/shared-icons': 2.0.9(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.0.10(react@18.3.1)
      '@nextui-org/use-aria-modal-overlay': 2.0.11(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-disclosure': 2.0.10(react@18.3.1)
      '@react-aria/dialog': 3.5.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/overlays': 3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/overlays': 3.6.7(react@18.3.1)
      '@react-types/overlays': 3.8.7(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/modal@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@nextui-org/use-aria-modal-overlay': 2.2.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-disclosure': 2.2.2(react@18.3.1)
      '@nextui-org/use-draggable': 2.1.2(react@18.3.1)
      '@react-aria/dialog': 3.5.20(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/overlays': 3.6.12(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/navbar@2.0.33(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(@types/react@18.3.5)(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/framer-utils': 2.0.21(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-toggle-button': 2.0.9(react@18.3.1)
      '@nextui-org/use-scroll-position': 2.0.6(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/overlays': 3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/toggle': 3.7.4(react@18.3.1)
      '@react-stately/utils': 3.10.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.5.10(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@nextui-org/navbar@2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-scroll-position': 2.1.1(react@18.3.1)
      '@react-aria/button': 3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/toggle': 3.8.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/pagination@2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-intersection-observer': 2.2.2(react@18.3.1)
      '@nextui-org/use-pagination': 2.2.3(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      scroll-into-view-if-needed: 3.0.10

  '@nextui-org/popover@2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/dialog': 3.5.20(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/overlays': 3.6.12(react@18.3.1)
      '@react-types/button': 3.10.1(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/progress@2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-is-mounted': 2.1.1(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/progress': 3.4.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/progress': 3.5.8(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/radio@2.1.4(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/radio': 3.10.4(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.12(react@18.3.1)
      '@react-stately/radio': 3.10.4(react@18.3.1)
      '@react-types/radio': 3.8.1(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/radio@2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/radio': 3.10.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/radio': 3.10.9(react@18.3.1)
      '@react-types/radio': 3.8.5(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/react-rsc-utils@2.0.12': {}

  '@nextui-org/react-rsc-utils@2.0.13': {}

  '@nextui-org/react-rsc-utils@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/react-utils@2.0.13(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.12
      '@nextui-org/shared-utils': 2.0.5
      react: 18.3.1

  '@nextui-org/react-utils@2.0.14(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.12
      '@nextui-org/shared-utils': 2.0.5
      react: 18.3.1

  '@nextui-org/react-utils@2.0.16(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.0.13
      '@nextui-org/shared-utils': 2.0.7
      react: 18.3.1

  '@nextui-org/react-utils@2.1.3(react@18.3.1)':
    dependencies:
      '@nextui-org/react-rsc-utils': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      react: 18.3.1

  '@nextui-org/react@2.6.11(@types/react@18.3.5)(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(tailwindcss@3.4.10)':
    dependencies:
      '@nextui-org/accordion': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/alert': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/autocomplete': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(@types/react@18.3.5)(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/avatar': 2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/badge': 2.2.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/breadcrumbs': 2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/calendar': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/card': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/checkbox': 2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/chip': 2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/code': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/date-input': 2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/date-picker': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/divider': 2.2.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/drawer': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/dropdown': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/image': 2.2.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/input': 2.4.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/input-otp': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/kbd': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/link': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/listbox': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/menu': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/modal': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/navbar': 2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/pagination': 2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/popover': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/progress': 2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/radio': 2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/ripple': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/scroll-shadow': 2.3.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/select': 2.4.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/skeleton': 2.2.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/slider': 2.4.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/snippet': 2.2.10(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/spacer': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/spinner': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/switch': 2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/table': 2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/tabs': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/tooltip': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/user': 2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - tailwindcss

  '@nextui-org/ripple@2.0.30(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/ripple@2.0.32(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/ripple@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/scroll-shadow@2.3.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-data-scroll-overflow': 2.2.2(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/select@2.4.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/form': 2.1.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/listbox': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/popover': 2.3.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/scroll-shadow': 2.3.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/spinner': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-aria-button': 2.2.4(react@18.3.1)
      '@nextui-org/use-aria-multiselect': 2.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/form': 3.0.11(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@tanstack/react-virtual': 3.11.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/shared-icons@2.0.8(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/shared-icons@2.0.9(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/shared-icons@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/shared-utils@2.0.5': {}

  '@nextui-org/shared-utils@2.0.7': {}

  '@nextui-org/shared-utils@2.1.2': {}

  '@nextui-org/skeleton@2.2.5(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/slider@2.4.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/tooltip': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/slider': 3.7.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/slider': 3.6.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - framer-motion

  '@nextui-org/snippet@2.0.38(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/button': 2.0.34(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-icons': 2.0.8(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/tooltip': 2.0.36(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-clipboard': 2.0.5(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/snippet@2.2.10(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/button': 2.2.9(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/tooltip': 2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/use-clipboard': 2.1.2(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/spacer@2.0.32(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/system-rsc': 2.1.5(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/spacer@2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system-rsc': 2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/spinner@2.0.30(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system-rsc': 2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/spinner@2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system-rsc': 2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/switch@2.0.31(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-safe-layout-effect': 2.0.5(react@18.3.1)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/switch': 3.6.4(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.12(react@18.3.1)
      '@react-stately/toggle': 3.7.4(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/switch@2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/switch': 3.6.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/toggle': 3.8.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/system-rsc@2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)':
    dependencies:
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      clsx: 1.2.1
      react: 18.3.1

  '@nextui-org/system-rsc@2.1.5(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)':
    dependencies:
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@react-types/shared': 3.23.1(react@18.3.1)
      clsx: 1.2.1
      react: 18.3.1

  '@nextui-org/system-rsc@2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)':
    dependencies:
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-types/shared': 3.26.0(react@18.3.1)
      clsx: 1.2.1
      react: 18.3.1

  '@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.5.5
      '@nextui-org/react-utils': 2.0.13(react@18.3.1)
      '@nextui-org/system-rsc': 2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@react-aria/i18n': 3.10.2(react@18.3.1)
      '@react-aria/overlays': 3.21.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/utils': 3.9.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/system@2.2.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.5.5
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/system-rsc': 2.1.2(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@react-aria/i18n': 3.11.1(react@18.3.1)
      '@react-aria/overlays': 3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/utils': 3.10.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/system@2.2.5(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.5.5
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/system-rsc': 2.1.5(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react@18.3.1)
      '@react-aria/i18n': 3.11.1(react@18.3.1)
      '@react-aria/overlays': 3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/utils': 3.10.1(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/system-rsc': 2.3.5(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/datepicker': 3.9.0(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@nextui-org/theme'

  '@nextui-org/table@2.0.39(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/checkbox': 2.1.4(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.0.16(react@18.3.1)
      '@nextui-org/shared-icons': 2.0.9(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.7
      '@nextui-org/spacer': 2.0.32(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/table': 3.14.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.12(react@18.3.1)
      '@react-stately/table': 3.11.8(react@18.3.1)
      '@react-stately/virtualizer': 3.7.1(react@18.3.1)
      '@react-types/grid': 3.2.6(react@18.3.1)
      '@react-types/table': 3.9.5(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/table@2.2.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/checkbox': 2.3.8(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-icons': 2.1.1(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/spacer': 2.2.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/table': 3.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/table': 3.13.0(react@18.3.1)
      '@react-stately/virtualizer': 4.2.0(react@18.3.1)
      '@react-types/grid': 3.2.10(react@18.3.1)
      '@react-types/table': 3.10.3(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/tabs@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-is-mounted': 2.1.1(react@18.3.1)
      '@nextui-org/use-update-effect': 2.1.1(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/tabs': 3.9.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/tabs': 3.7.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@react-types/tabs': 3.3.11(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      scroll-into-view-if-needed: 3.0.10

  '@nextui-org/theme@2.2.5(tailwindcss@3.4.10)':
    dependencies:
      clsx: 1.2.1
      color: 4.2.3
      color2k: 2.0.3
      deepmerge: 4.3.1
      flat: 5.0.2
      lodash.foreach: 4.5.0
      lodash.get: 4.4.2
      lodash.kebabcase: 4.1.1
      lodash.mapkeys: 4.6.0
      lodash.omit: 4.5.0
      tailwind-merge: 1.14.0
      tailwind-variants: 0.1.20(tailwindcss@3.4.10)
      tailwindcss: 3.4.10

  '@nextui-org/theme@2.4.5(tailwindcss@3.4.10)':
    dependencies:
      '@nextui-org/shared-utils': 2.1.2
      clsx: 1.2.1
      color: 4.2.3
      color2k: 2.0.3
      deepmerge: 4.3.1
      flat: 5.0.2
      tailwind-merge: 2.6.0
      tailwind-variants: 0.1.20(tailwindcss@3.4.10)
      tailwindcss: 3.4.10

  '@nextui-org/tooltip@2.0.36(@nextui-org/system@2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.0.21(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/framer-utils': 2.0.21(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.0.14(react@18.3.1)
      '@nextui-org/shared-utils': 2.0.5
      '@nextui-org/system': 2.2.1(@nextui-org/theme@2.2.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.2.5(tailwindcss@3.4.10)
      '@nextui-org/use-safe-layout-effect': 2.0.5(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/overlays': 3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/tooltip': 3.7.4(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/tooltip': 3.4.9(react@18.3.1)
      '@react-types/overlays': 3.8.7(react@18.3.1)
      '@react-types/tooltip': 3.4.9(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/tooltip@2.2.7(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/aria-utils': 2.2.7(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/dom-animation': 2.1.1(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))
      '@nextui-org/framer-utils': 2.1.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/tooltip': 3.7.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/tooltip': 3.5.0(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      '@react-types/tooltip': 3.4.13(react@18.3.1)
      framer-motion: 12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/use-aria-accordion@2.2.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/button': 3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/selection': 3.21.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/tree': 3.8.6(react@18.3.1)
      '@react-types/accordion': 3.0.0-alpha.25(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@nextui-org/use-aria-button@2.0.10(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-types/button': 3.9.4(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-aria-button@2.0.9(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-types/button': 3.9.4(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-aria-button@2.2.4(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.1.2
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/button': 3.10.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-aria-link@2.0.18(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.17.1(react@18.3.1)
      '@react-aria/interactions': 3.21.3(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-types/link': 3.5.5(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-aria-link@2.2.5(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.1.2
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/link': 3.5.9(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-aria-modal-overlay@2.0.11(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/overlays': 3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/overlays': 3.6.7(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/use-aria-modal-overlay@2.2.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/overlays': 3.6.12(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/use-aria-multiselect@2.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/label': 3.7.13(react@18.3.1)
      '@react-aria/listbox': 3.13.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/menu': 3.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.21.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/form': 3.1.0(react@18.3.1)
      '@react-stately/list': 3.11.1(react@18.3.1)
      '@react-stately/menu': 3.9.0(react@18.3.1)
      '@react-types/button': 3.10.1(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      '@react-types/select': 3.9.8(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nextui-org/use-aria-toggle-button@2.0.9(react@18.3.1)':
    dependencies:
      '@nextui-org/use-aria-button': 2.0.9(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/toggle': 3.7.4(react@18.3.1)
      '@react-types/button': 3.9.4(react@18.3.1)
      '@react-types/shared': 3.23.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-callback-ref@2.0.6(react@18.3.1)':
    dependencies:
      '@nextui-org/use-safe-layout-effect': 2.0.6(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-callback-ref@2.1.1(react@18.3.1)':
    dependencies:
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-clipboard@2.0.5(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-clipboard@2.1.2(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-data-scroll-overflow@2.2.2(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.1.2
      react: 18.3.1

  '@nextui-org/use-disclosure@2.0.10(react@18.3.1)':
    dependencies:
      '@nextui-org/use-callback-ref': 2.0.6(react@18.3.1)
      '@react-aria/utils': 3.24.1(react@18.3.1)
      '@react-stately/utils': 3.10.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-disclosure@2.2.2(react@18.3.1)':
    dependencies:
      '@nextui-org/use-callback-ref': 2.1.1(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-draggable@2.1.2(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-image@2.1.2(react@18.3.1)':
    dependencies:
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/use-safe-layout-effect': 2.1.1(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-intersection-observer@2.2.2(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/ssr': 3.9.7(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-is-mobile@2.0.7(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-is-mobile@2.2.2(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.7(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-is-mounted@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-measure@2.0.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-measure@2.0.2(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-measure@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-pagination@2.2.3(react@18.3.1)':
    dependencies:
      '@nextui-org/shared-utils': 2.1.2
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      react: 18.3.1

  '@nextui-org/use-safe-layout-effect@2.0.5(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-safe-layout-effect@2.0.6(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-safe-layout-effect@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-scroll-position@2.0.6(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-scroll-position@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/use-update-effect@2.1.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@nextui-org/user@2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@nextui-org/avatar': 2.2.6(@nextui-org/system@2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/react-utils': 2.1.3(react@18.3.1)
      '@nextui-org/shared-utils': 2.1.2
      '@nextui-org/system': 2.4.6(@nextui-org/theme@2.4.5(tailwindcss@3.4.10))(framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@nextui-org/theme': 2.4.5(tailwindcss@3.4.10)
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1

  '@nolyfill/is-core-module@1.0.39': {}

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@react-aria/breadcrumbs@3.5.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/link': 3.8.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/breadcrumbs': 3.7.9(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/button@3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/toolbar': 3.0.0-beta.11(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/toggle': 3.8.3(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/button@3.9.5(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/toggle': 3.7.7(react@18.3.1)
      '@react-types/button': 3.9.6(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/calendar@3.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/live-announcer': 3.4.2
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/calendar': 3.6.0(react@18.3.1)
      '@react-types/button': 3.10.1(react@18.3.1)
      '@react-types/calendar': 3.5.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/checkbox@3.14.3(react@18.3.1)':
    dependencies:
      '@react-aria/form': 3.0.8(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/label': 3.7.11(react@18.3.1)
      '@react-aria/toggle': 3.10.7(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/checkbox': 3.6.5(react@18.3.1)
      '@react-stately/form': 3.0.5(react@18.3.1)
      '@react-stately/toggle': 3.7.7(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/checkbox@3.15.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/form': 3.0.15(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/toggle': 3.11.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/checkbox': 3.6.10(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/toggle': 3.8.0(react@18.3.1)
      '@react-types/checkbox': 3.9.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/combobox@3.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/listbox': 3.14.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/live-announcer': 3.4.2
      '@react-aria/menu': 3.18.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/overlays': 3.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/textfield': 3.17.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/combobox': 3.10.1(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/combobox': 3.13.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/datepicker@3.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@internationalized/number': 3.6.1
      '@internationalized/string': 3.2.6
      '@react-aria/focus': 3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/form': 3.0.15(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/spinbutton': 3.6.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/datepicker': 3.11.0(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/calendar': 3.7.0(react@18.3.1)
      '@react-types/datepicker': 3.9.0(react@18.3.1)
      '@react-types/dialog': 3.5.17(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/dialog@3.5.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/overlays': 3.23.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/dialog': 3.5.12(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/dialog@3.5.20(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/overlays': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/dialog': 3.5.17(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/focus@3.16.2(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/focus@3.17.1(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/focus@3.18.2(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/focus@3.19.0(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/focus@3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/form@3.0.11(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/form@3.0.15(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/form@3.0.8(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/form': 3.0.5(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/grid@3.10.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/live-announcer': 3.3.4
      '@react-aria/selection': 3.19.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/grid': 3.9.2(react@18.3.1)
      '@react-stately/selection': 3.16.2(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@react-types/grid': 3.2.8(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/grid@3.13.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/live-announcer': 3.4.2
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/grid': 3.11.1(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-types/checkbox': 3.9.3(react@18.3.1)
      '@react-types/grid': 3.3.1(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/i18n@3.10.2(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.5.5
      '@internationalized/message': 3.1.4
      '@internationalized/number': 3.5.3
      '@internationalized/string': 3.2.3
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/i18n@3.11.1(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.5.5
      '@internationalized/message': 3.1.4
      '@internationalized/number': 3.5.3
      '@internationalized/string': 3.2.3
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/i18n@3.12.2(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.5.5
      '@internationalized/message': 3.1.4
      '@internationalized/number': 3.5.3
      '@internationalized/string': 3.2.3
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/i18n@3.12.4(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.8.0
      '@internationalized/message': 3.1.7
      '@internationalized/number': 3.6.1
      '@internationalized/string': 3.2.6
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/i18n@3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.8.0
      '@internationalized/message': 3.1.7
      '@internationalized/number': 3.6.1
      '@internationalized/string': 3.2.6
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/interactions@3.21.1(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/interactions@3.21.3(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/interactions@3.22.2(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/interactions@3.22.5(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/interactions@3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/flags': 3.1.1
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/label@3.7.11(react@18.3.1)':
    dependencies:
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/label@3.7.13(react@18.3.1)':
    dependencies:
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/label@3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/link@3.7.1(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/link': 3.5.7(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/link@3.7.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/link': 3.5.9(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/link@3.8.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/link': 3.6.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/listbox@3.11.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/label': 3.7.11(react@18.3.1)
      '@react-aria/selection': 3.19.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/list': 3.10.8(react@18.3.1)
      '@react-types/listbox': 3.5.1(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/listbox@3.13.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/list': 3.11.1(react@18.3.1)
      '@react-types/listbox': 3.6.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/listbox@3.14.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/list': 3.12.1(react@18.3.1)
      '@react-types/listbox': 3.6.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/live-announcer@3.3.4':
    dependencies:
      '@swc/helpers': 0.5.12

  '@react-aria/live-announcer@3.4.2':
    dependencies:
      '@swc/helpers': 0.5.12

  '@react-aria/menu@3.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/overlays': 3.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/menu': 3.9.0(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/tree': 3.8.6(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/menu': 3.9.13(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/menu@3.18.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/overlays': 3.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/menu': 3.9.3(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/tree': 3.8.9(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/menu': 3.10.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/overlays@3.21.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.15(react@18.3.1)
      '@react-stately/overlays': 3.6.10(react@18.3.1)
      '@react-types/button': 3.9.6(react@18.3.1)
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/overlays@3.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.15(react@18.3.1)
      '@react-stately/overlays': 3.6.10(react@18.3.1)
      '@react-types/button': 3.9.6(react@18.3.1)
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/overlays@3.23.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.15(react@18.3.1)
      '@react-stately/overlays': 3.6.10(react@18.3.1)
      '@react-types/button': 3.9.6(react@18.3.1)
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/overlays@3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/overlays': 3.6.12(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/overlays@3.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.22(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/overlays': 3.8.14(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/progress@3.4.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/progress': 3.5.8(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/radio@3.10.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/form': 3.0.15(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/radio': 3.10.9(react@18.3.1)
      '@react-types/radio': 3.8.5(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/radio@3.10.4(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/form': 3.0.8(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/label': 3.7.11(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/radio': 3.10.4(react@18.3.1)
      '@react-types/radio': 3.8.1(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/selection@3.19.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/selection': 3.16.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/selection@3.21.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/selection@3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/slider@3.7.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.4(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/slider': 3.6.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@react-types/slider': 3.7.10(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/spinbutton@3.6.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/live-announcer': 3.4.2
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/button': 3.12.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/ssr@3.9.5(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/ssr@3.9.7(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/ssr@3.9.8(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/switch@3.6.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/toggle': 3.11.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/toggle': 3.8.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@react-types/switch': 3.5.10(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/switch@3.6.4(react@18.3.1)':
    dependencies:
      '@react-aria/toggle': 3.10.7(react@18.3.1)
      '@react-stately/toggle': 3.7.7(react@18.3.1)
      '@react-types/switch': 3.5.5(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/table@3.14.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/grid': 3.10.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/live-announcer': 3.3.4
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.15(react@18.3.1)
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/flags': 3.0.3
      '@react-stately/table': 3.11.8(react@18.3.1)
      '@react-stately/virtualizer': 3.7.1(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@react-types/grid': 3.2.8(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@react-types/table': 3.9.5(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/table@3.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/grid': 3.13.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/live-announcer': 3.4.2
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-aria/visually-hidden': 3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/flags': 3.1.1
      '@react-stately/table': 3.13.0(react@18.3.1)
      '@react-types/checkbox': 3.9.3(react@18.3.1)
      '@react-types/grid': 3.2.10(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@react-types/table': 3.10.3(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/tabs@3.9.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/selection': 3.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/tabs': 3.7.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@react-types/tabs': 3.3.11(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/textfield@3.14.5(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/form': 3.0.8(react@18.3.1)
      '@react-aria/label': 3.7.11(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/form': 3.0.5(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@react-types/textfield': 3.9.6(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/textfield@3.15.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/form': 3.0.15(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@react-types/textfield': 3.10.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/textfield@3.17.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/form': 3.0.15(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/label': 3.7.17(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@react-types/textfield': 3.12.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/toggle@3.10.7(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/toggle': 3.7.7(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/toggle@3.11.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/toggle': 3.8.3(react@18.3.1)
      '@react-types/checkbox': 3.9.3(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/toolbar@3.0.0-beta.11(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.19.0(react@18.3.1)
      '@react-aria/i18n': 3.12.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/tooltip@3.7.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.20.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.22.5(react@18.3.1)
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-stately/tooltip': 3.5.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@react-types/tooltip': 3.4.13(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/tooltip@3.7.4(react@18.3.1)':
    dependencies:
      '@react-aria/focus': 3.18.2(react@18.3.1)
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-stately/tooltip': 3.4.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@react-types/tooltip': 3.4.9(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/utils@3.24.1(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/utils@3.25.2(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.5(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/utils@3.26.0(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1

  '@react-aria/utils@3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.8(react@18.3.1)
      '@react-stately/flags': 3.1.1
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      clsx: 2.1.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/visually-hidden@3.8.12(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/visually-hidden@3.8.15(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.22.2(react@18.3.1)
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-aria/visually-hidden@3.8.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
    transitivePeerDependencies:
      - react-dom

  '@react-aria/visually-hidden@3.8.22(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.28.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-stately/calendar@3.6.0(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/calendar': 3.5.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/checkbox@3.6.10(react@18.3.1)':
    dependencies:
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/checkbox': 3.9.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/checkbox@3.6.5(react@18.3.1)':
    dependencies:
      '@react-stately/form': 3.0.5(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/collections@3.10.5(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/collections@3.10.7(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/collections@3.10.9(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/collections@3.12.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/collections@3.12.3(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/combobox@3.10.1(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/list': 3.12.1(react@18.3.1)
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-stately/select': 3.6.12(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/combobox': 3.13.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/datepicker@3.11.0(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@internationalized/string': 3.2.6
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/datepicker': 3.9.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/flags@3.0.3':
    dependencies:
      '@swc/helpers': 0.5.12

  '@react-stately/flags@3.1.1':
    dependencies:
      '@swc/helpers': 0.5.12

  '@react-stately/form@3.0.5(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/form@3.1.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/form@3.1.3(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/grid@3.11.1(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-types/grid': 3.3.1(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/grid@3.9.2(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/selection': 3.16.2(react@18.3.1)
      '@react-types/grid': 3.2.8(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/list@3.10.3(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/selection': 3.16.2(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/list@3.10.8(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/selection': 3.16.2(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/list@3.11.1(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/list@3.12.1(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/menu@3.9.0(react@18.3.1)':
    dependencies:
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-types/menu': 3.9.13(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/menu@3.9.3(react@18.3.1)':
    dependencies:
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-types/menu': 3.10.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/overlays@3.6.10(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/overlays@3.6.12(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/overlays': 3.8.11(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/overlays@3.6.15(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/overlays': 3.8.14(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/overlays@3.6.5(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/overlays@3.6.7(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/radio@3.10.4(react@18.3.1)':
    dependencies:
      '@react-stately/form': 3.0.5(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/radio': 3.8.1(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/radio@3.10.9(react@18.3.1)':
    dependencies:
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/radio': 3.8.5(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/select@3.6.12(react@18.3.1)':
    dependencies:
      '@react-stately/form': 3.1.3(react@18.3.1)
      '@react-stately/list': 3.12.1(react@18.3.1)
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-types/select': 3.9.11(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/selection@3.16.2(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/selection@3.20.1(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/slider@3.6.0(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@react-types/slider': 3.7.10(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/table@3.11.8(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.10.9(react@18.3.1)
      '@react-stately/flags': 3.0.3
      '@react-stately/grid': 3.9.2(react@18.3.1)
      '@react-stately/selection': 3.16.2(react@18.3.1)
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/grid': 3.2.8(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@react-types/table': 3.9.5(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/table@3.13.0(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/flags': 3.1.1
      '@react-stately/grid': 3.11.1(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/grid': 3.2.10(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@react-types/table': 3.10.3(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/tabs@3.7.0(react@18.3.1)':
    dependencies:
      '@react-stately/list': 3.12.1(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@react-types/tabs': 3.3.11(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/toggle@3.7.4(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/toggle@3.7.7(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.3(react@18.3.1)
      '@react-types/checkbox': 3.8.3(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/toggle@3.8.0(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.5(react@18.3.1)
      '@react-types/checkbox': 3.9.3(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/toggle@3.8.3(react@18.3.1)':
    dependencies:
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/checkbox': 3.9.3(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/tooltip@3.4.9(react@18.3.1)':
    dependencies:
      '@react-stately/overlays': 3.6.10(react@18.3.1)
      '@react-types/tooltip': 3.4.9(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/tooltip@3.5.0(react@18.3.1)':
    dependencies:
      '@react-stately/overlays': 3.6.15(react@18.3.1)
      '@react-types/tooltip': 3.4.13(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/tree@3.8.6(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/tree@3.8.9(react@18.3.1)':
    dependencies:
      '@react-stately/collections': 3.12.3(react@18.3.1)
      '@react-stately/selection': 3.20.1(react@18.3.1)
      '@react-stately/utils': 3.10.6(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/utils@3.10.1(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/utils@3.10.3(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/utils@3.10.5(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/utils@3.10.6(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/utils@3.9.1(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/virtualizer@3.7.1(react@18.3.1)':
    dependencies:
      '@react-aria/utils': 3.25.2(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-stately/virtualizer@4.2.0(react@18.3.1)':
    dependencies:
      '@react-aria/utils': 3.26.0(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      '@swc/helpers': 0.5.12
      react: 18.3.1

  '@react-types/accordion@3.0.0-alpha.25(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/breadcrumbs@3.7.9(react@18.3.1)':
    dependencies:
      '@react-types/link': 3.6.0(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/button@3.10.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/button@3.12.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/button@3.9.4(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/button@3.9.6(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/calendar@3.5.0(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/calendar@3.7.0(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.8.0
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/checkbox@3.8.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/checkbox@3.8.3(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/checkbox@3.9.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/checkbox@3.9.3(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/combobox@3.13.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/datepicker@3.9.0(react@18.3.1)':
    dependencies:
      '@internationalized/date': 3.6.0
      '@react-types/calendar': 3.7.0(react@18.3.1)
      '@react-types/overlays': 3.8.14(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/dialog@3.5.12(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/dialog@3.5.17(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.14(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/form@3.7.8(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/grid@3.2.10(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/grid@3.2.6(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/grid@3.2.8(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/grid@3.3.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/link@3.5.5(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/link@3.5.7(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/link@3.5.9(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/link@3.6.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/listbox@3.5.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/listbox@3.6.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/menu@3.10.0(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.14(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/menu@3.9.13(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.14(react@18.3.1)
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/menu@3.9.7(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/overlays@3.8.11(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/overlays@3.8.14(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/overlays@3.8.5(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/overlays@3.8.7(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/overlays@3.8.9(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/progress@3.5.8(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/radio@3.8.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/radio@3.8.5(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/select@3.9.11(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/select@3.9.8(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/shared@3.22.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@react-types/shared@3.23.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@react-types/shared@3.24.1(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@react-types/shared@3.26.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@react-types/shared@3.29.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@react-types/slider@3.7.10(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/switch@3.5.10(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/switch@3.5.5(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/table@3.10.3(react@18.3.1)':
    dependencies:
      '@react-types/grid': 3.2.10(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/table@3.9.5(react@18.3.1)':
    dependencies:
      '@react-types/grid': 3.2.8(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/tabs@3.3.11(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/textfield@3.10.0(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.26.0(react@18.3.1)
      react: 18.3.1

  '@react-types/textfield@3.12.1(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/textfield@3.9.3(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/textfield@3.9.6(react@18.3.1)':
    dependencies:
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@react-types/tooltip@3.4.13(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.11(react@18.3.1)
      '@react-types/shared': 3.29.0(react@18.3.1)
      react: 18.3.1

  '@react-types/tooltip@3.4.9(react@18.3.1)':
    dependencies:
      '@react-types/overlays': 3.8.9(react@18.3.1)
      '@react-types/shared': 3.24.1(react@18.3.1)
      react: 18.3.1

  '@rushstack/eslint-patch@1.10.4': {}

  '@swc/counter@0.1.3': {}

  '@swc/helpers@0.5.12':
    dependencies:
      tslib: 2.7.0

  '@swc/helpers@0.5.5':
    dependencies:
      '@swc/counter': 0.1.3
      tslib: 2.7.0

  '@tanstack/react-virtual@3.10.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@tanstack/virtual-core': 3.10.6
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@tanstack/react-virtual@3.11.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@tanstack/virtual-core': 3.11.2
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@tanstack/virtual-core@3.10.6': {}

  '@tanstack/virtual-core@3.11.2': {}

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 0.7.34

  '@types/estree-jsx@1.0.5':
    dependencies:
      '@types/estree': 1.0.6

  '@types/estree@1.0.6': {}

  '@types/hast@3.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/js-cookie@2.2.7': {}

  '@types/json5@0.0.29': {}

  '@types/lodash.debounce@4.0.9':
    dependencies:
      '@types/lodash': 4.17.7

  '@types/lodash@4.17.7': {}

  '@types/mdast@4.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/ms@0.7.34': {}

  '@types/node@20.16.2':
    dependencies:
      undici-types: 6.19.8

  '@types/prop-types@15.7.12': {}

  '@types/react-dom@18.3.0':
    dependencies:
      '@types/react': 18.3.5

  '@types/react@18.3.5':
    dependencies:
      '@types/prop-types': 15.7.12
      csstype: 3.1.3

  '@types/unist@2.0.11': {}

  '@types/unist@3.0.3': {}

  '@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4)':
    dependencies:
      '@typescript-eslint/scope-manager': 7.2.0
      '@typescript-eslint/types': 7.2.0
      '@typescript-eslint/typescript-estree': 7.2.0(typescript@5.5.4)
      '@typescript-eslint/visitor-keys': 7.2.0
      debug: 4.3.6
      eslint: 8.57.0
    optionalDependencies:
      typescript: 5.5.4
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@7.2.0':
    dependencies:
      '@typescript-eslint/types': 7.2.0
      '@typescript-eslint/visitor-keys': 7.2.0

  '@typescript-eslint/types@7.2.0': {}

  '@typescript-eslint/typescript-estree@7.2.0(typescript@5.5.4)':
    dependencies:
      '@typescript-eslint/types': 7.2.0
      '@typescript-eslint/visitor-keys': 7.2.0
      debug: 4.3.6
      globby: 11.1.0
      is-glob: 4.0.3
      minimatch: 9.0.3
      semver: 7.6.3
      ts-api-utils: 1.3.0(typescript@5.5.4)
    optionalDependencies:
      typescript: 5.5.4
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@7.2.0':
    dependencies:
      '@typescript-eslint/types': 7.2.0
      eslint-visitor-keys: 3.4.3

  '@ungap/structured-clone@1.2.0': {}

  '@xobotyi/scrollbar-width@1.9.5': {}

  acorn-jsx@5.3.2(acorn@8.12.1):
    dependencies:
      acorn: 8.12.1

  acorn@8.12.1: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-query@5.1.3:
    dependencies:
      deep-equal: 2.2.3

  array-buffer-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      is-string: 1.0.7

  array-union@2.1.0: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.findlastindex@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-shim-unscopables: 1.0.2

  arraybuffer.prototype.slice@1.0.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3

  ast-types-flow@0.0.8: {}

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  axe-core@4.10.0: {}

  axobject-query@3.1.1:
    dependencies:
      deep-equal: 2.2.3

  bail@2.0.2: {}

  balanced-match@1.0.2: {}

  binary-extensions@2.3.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  bufferutil@4.0.8:
    dependencies:
      node-gyp-build: 4.8.2

  busboy@1.6.0:
    dependencies:
      streamsearch: 1.1.0

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  caniuse-lite@1.0.30001655: {}

  ccount@2.0.1: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  character-entities-html4@2.1.0: {}

  character-entities-legacy@3.0.0: {}

  character-entities@2.0.2: {}

  character-reference-invalid@2.0.1: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  client-only@0.0.1: {}

  clsx@1.2.1: {}

  clsx@2.1.1: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color2k@2.0.3: {}

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1

  comma-separated-tokens@2.0.3: {}

  commander@4.1.1: {}

  compute-scroll-into-view@3.1.0: {}

  concat-map@0.0.1: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-in-js-utils@3.1.0:
    dependencies:
      hyphenate-style-name: 1.1.0

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  d@1.0.2:
    dependencies:
      es5-ext: 0.10.64
      type: 2.7.3

  damerau-levenshtein@1.0.8: {}

  data-view-buffer@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-offset@1.0.0:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.3.6:
    dependencies:
      ms: 2.1.2

  decode-named-character-reference@1.0.2:
    dependencies:
      character-entities: 2.0.2

  deep-equal@2.2.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      es-get-iterator: 1.1.3
      get-intrinsic: 1.2.4
      is-arguments: 1.1.1
      is-array-buffer: 3.0.4
      is-date-object: 1.0.5
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      isarray: 2.0.5
      object-is: 1.1.6
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      side-channel: 1.0.6
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  dequal@2.0.3: {}

  detect-node-es@1.1.0: {}

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  didyoumean@1.2.2: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  eastasianwidth@0.2.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  enhanced-resolve@5.17.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  eruda@3.3.0: {}

  es-abstract@1.23.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.2
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es-get-iterator@1.1.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      is-arguments: 1.1.1
      is-map: 2.0.3
      is-set: 2.0.3
      is-string: 1.0.7
      isarray: 2.0.5
      stop-iteration-iterator: 1.0.0

  es-iterator-helpers@1.0.19:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-set-tostringtag: 2.0.3
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      iterator.prototype: 1.1.2
      safe-array-concat: 1.1.2

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  es5-ext@0.10.64:
    dependencies:
      es6-iterator: 2.0.3
      es6-symbol: 3.1.4
      esniff: 2.0.1
      next-tick: 1.1.0

  es6-iterator@2.0.3:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      es6-symbol: 3.1.4

  es6-symbol@3.1.4:
    dependencies:
      d: 1.0.2
      ext: 1.7.0

  escape-string-regexp@4.0.0: {}

  eslint-config-next@14.2.3(eslint@8.57.0)(typescript@5.5.4):
    dependencies:
      '@next/eslint-plugin-next': 14.2.3
      '@rushstack/eslint-patch': 1.10.4
      '@typescript-eslint/parser': 7.2.0(eslint@8.57.0)(typescript@5.5.4)
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0)
      eslint-plugin-import: 2.29.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0)
      eslint-plugin-jsx-a11y: 6.9.0(eslint@8.57.0)
      eslint-plugin-react: 7.35.0(eslint@8.57.0)
      eslint-plugin-react-hooks: 4.6.2(eslint@8.57.0)
    optionalDependencies:
      typescript: 5.5.4
    transitivePeerDependencies:
      - eslint-import-resolver-webpack
      - eslint-plugin-import-x
      - supports-color

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.15.1
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0):
    dependencies:
      '@nolyfill/is-core-module': 1.0.39
      debug: 4.3.6
      enhanced-resolve: 5.17.1
      eslint: 8.57.0
      eslint-module-utils: 2.8.2(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0)
      fast-glob: 3.3.2
      get-tsconfig: 4.8.0
      is-bun-module: 1.1.0
      is-glob: 4.0.3
    optionalDependencies:
      eslint-plugin-import: 2.29.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0)
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint-import-resolver-node
      - eslint-import-resolver-webpack
      - supports-color

  eslint-module-utils@2.8.2(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 7.2.0(eslint@8.57.0)(typescript@5.5.4)
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0)
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-import@2.29.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.2
      array.prototype.flatmap: 1.3.2
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.8.2(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0)
      hasown: 2.0.2
      is-core-module: 2.15.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.0
      semver: 6.3.1
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 7.2.0(eslint@8.57.0)(typescript@5.5.4)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-jsx-a11y@6.9.0(eslint@8.57.0):
    dependencies:
      aria-query: 5.1.3
      array-includes: 3.1.8
      array.prototype.flatmap: 1.3.2
      ast-types-flow: 0.0.8
      axe-core: 4.10.0
      axobject-query: 3.1.1
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      es-iterator-helpers: 1.0.19
      eslint: 8.57.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.0.3
      string.prototype.includes: 2.0.0

  eslint-plugin-react-hooks@4.6.2(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0

  eslint-plugin-react@7.35.0(eslint@8.57.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.2
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.0.19
      eslint: 8.57.0
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.values: 1.2.0
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.11
      string.prototype.repeat: 1.0.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint@8.57.0:
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.57.0)
      '@eslint-community/regexpp': 4.11.0
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.0
      '@humanwhocodes/config-array': 0.11.14
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.2.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.6
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  esniff@2.0.1:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      event-emitter: 0.3.5
      type: 2.7.3

  espree@9.6.1:
    dependencies:
      acorn: 8.12.1
      acorn-jsx: 5.3.2(acorn@8.12.1)
      eslint-visitor-keys: 3.4.3

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-util-is-identifier-name@3.0.0: {}

  esutils@2.0.3: {}

  event-emitter@0.3.5:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64

  ext@1.7.0:
    dependencies:
      type: 2.7.3

  extend@3.0.2: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.2:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-shallow-equal@1.0.0: {}

  fastest-stable-stringify@2.0.2: {}

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.1
      keyv: 4.5.4
      rimraf: 3.0.2

  flat@5.0.2: {}

  flatted@3.3.1: {}

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.0:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  framer-motion@12.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      motion-dom: 12.11.0
      motion-utils: 12.9.4
      tslib: 2.7.0
    optionalDependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3

  functions-have-names@1.2.3: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-nonce@1.0.1: {}

  get-symbol-description@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4

  get-tsconfig@4.8.0:
    dependencies:
      resolve-pkg-maps: 1.0.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.3.10:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 2.3.6
      minimatch: 9.0.5
      minipass: 7.1.2
      path-scurry: 1.11.1

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.0
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-bigints@1.0.2: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hast-util-to-jsx-runtime@2.3.2:
    dependencies:
      '@types/estree': 1.0.6
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      hast-util-whitespace: 3.0.0
      mdast-util-mdx-expression: 2.0.1
      mdast-util-mdx-jsx: 3.1.3
      mdast-util-mdxjs-esm: 2.0.1
      property-information: 6.5.0
      space-separated-tokens: 2.0.2
      style-to-object: 1.0.8
      unist-util-position: 5.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  hast-util-whitespace@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  html-url-attributes@3.0.1: {}

  hyphenate-style-name@1.1.0: {}

  ignore@5.3.2: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inline-style-parser@0.2.4: {}

  inline-style-prefixer@7.0.1:
    dependencies:
      css-in-js-utils: 3.1.0

  input-otp@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  internal-slot@1.0.7:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6

  intl-messageformat@10.5.14:
    dependencies:
      '@formatjs/ecma402-abstract': 2.0.0
      '@formatjs/fast-memoize': 2.2.0
      '@formatjs/icu-messageformat-parser': 2.7.8
      tslib: 2.7.0

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  is-alphabetical@2.0.1: {}

  is-alphanumerical@2.0.1:
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.4:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  is-arrayish@0.3.2: {}

  is-async-function@2.0.0:
    dependencies:
      has-tostringtag: 1.0.2

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-bun-module@1.1.0:
    dependencies:
      semver: 7.6.3

  is-callable@1.2.7: {}

  is-core-module@2.15.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.1:
    dependencies:
      is-typed-array: 1.1.13

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-decimal@2.0.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.0.10:
    dependencies:
      has-tostringtag: 1.0.2

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-hexadecimal@2.0.1: {}

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-plain-obj@4.1.0: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.3:
    dependencies:
      call-bind: 1.0.7

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-typed-array@1.1.13:
    dependencies:
      which-typed-array: 1.1.15

  is-typedarray@1.0.0: {}

  is-weakmap@2.0.2: {}

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-weakset@2.0.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  iterator.prototype@1.1.2:
    dependencies:
      define-properties: 1.2.1
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      reflect.getprototypeof: 1.0.6
      set-function-name: 2.0.2

  jackspeak@2.3.6:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jiti@1.21.6: {}

  js-audio-recorder@1.0.7: {}

  js-cookie@2.2.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.2
      object.assign: 4.1.5
      object.values: 1.2.0

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  language-subtag-registry@0.3.23: {}

  language-tags@1.0.9:
    dependencies:
      language-subtag-registry: 0.3.23

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@2.1.0: {}

  lilconfig@3.1.2: {}

  lines-and-columns@1.2.4: {}

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.foreach@4.5.0: {}

  lodash.get@4.4.2: {}

  lodash.kebabcase@4.1.1: {}

  lodash.mapkeys@4.6.0: {}

  lodash.merge@4.6.2: {}

  lodash.omit@4.5.0: {}

  lodash@4.17.21: {}

  longest-streak@3.1.0: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@10.4.3: {}

  mdast-util-from-markdown@2.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      decode-named-character-reference: 1.0.2
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.0
      micromark-util-decode-numeric-character-reference: 2.0.1
      micromark-util-decode-string: 2.0.0
      micromark-util-normalize-identifier: 2.0.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-expression@2.0.1:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.1
      mdast-util-to-markdown: 2.1.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-jsx@3.1.3:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.1
      mdast-util-to-markdown: 2.1.0
      parse-entities: 4.0.1
      stringify-entities: 4.0.4
      unist-util-stringify-position: 4.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdxjs-esm@2.0.1:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.1
      mdast-util-to-markdown: 2.1.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-phrasing@4.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      unist-util-is: 6.0.0

  mdast-util-to-hast@13.2.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@ungap/structured-clone': 1.2.0
      devlop: 1.1.0
      micromark-util-sanitize-uri: 2.0.0
      trim-lines: 3.0.1
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3

  mdast-util-to-markdown@2.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-decode-string: 2.0.0
      unist-util-visit: 5.0.0
      zwitch: 2.0.4

  mdast-util-to-string@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4

  mdn-data@2.0.14: {}

  merge2@1.4.1: {}

  micromark-core-commonmark@2.0.1:
    dependencies:
      decode-named-character-reference: 1.0.2
      devlop: 1.1.0
      micromark-factory-destination: 2.0.0
      micromark-factory-label: 2.0.0
      micromark-factory-space: 2.0.0
      micromark-factory-title: 2.0.0
      micromark-factory-whitespace: 2.0.0
      micromark-util-character: 2.1.0
      micromark-util-chunked: 2.0.0
      micromark-util-classify-character: 2.0.0
      micromark-util-html-tag-name: 2.0.0
      micromark-util-normalize-identifier: 2.0.0
      micromark-util-resolve-all: 2.0.0
      micromark-util-subtokenize: 2.0.1
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-factory-destination@2.0.0:
    dependencies:
      micromark-util-character: 2.1.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-factory-label@2.0.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-factory-space@2.0.0:
    dependencies:
      micromark-util-character: 2.1.0
      micromark-util-types: 2.0.0

  micromark-factory-title@2.0.0:
    dependencies:
      micromark-factory-space: 2.0.0
      micromark-util-character: 2.1.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-factory-whitespace@2.0.0:
    dependencies:
      micromark-factory-space: 2.0.0
      micromark-util-character: 2.1.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-util-character@2.1.0:
    dependencies:
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-util-chunked@2.0.0:
    dependencies:
      micromark-util-symbol: 2.0.0

  micromark-util-classify-character@2.0.0:
    dependencies:
      micromark-util-character: 2.1.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-util-combine-extensions@2.0.0:
    dependencies:
      micromark-util-chunked: 2.0.0
      micromark-util-types: 2.0.0

  micromark-util-decode-numeric-character-reference@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.0

  micromark-util-decode-string@2.0.0:
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-util-character: 2.1.0
      micromark-util-decode-numeric-character-reference: 2.0.1
      micromark-util-symbol: 2.0.0

  micromark-util-encode@2.0.0: {}

  micromark-util-html-tag-name@2.0.0: {}

  micromark-util-normalize-identifier@2.0.0:
    dependencies:
      micromark-util-symbol: 2.0.0

  micromark-util-resolve-all@2.0.0:
    dependencies:
      micromark-util-types: 2.0.0

  micromark-util-sanitize-uri@2.0.0:
    dependencies:
      micromark-util-character: 2.1.0
      micromark-util-encode: 2.0.0
      micromark-util-symbol: 2.0.0

  micromark-util-subtokenize@2.0.1:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.0
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0

  micromark-util-symbol@2.0.0: {}

  micromark-util-types@2.0.0: {}

  micromark@4.0.0:
    dependencies:
      '@types/debug': 4.1.12
      debug: 4.3.6
      decode-named-character-reference: 1.0.2
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.1
      micromark-factory-space: 2.0.0
      micromark-util-character: 2.1.0
      micromark-util-chunked: 2.0.0
      micromark-util-combine-extensions: 2.0.0
      micromark-util-decode-numeric-character-reference: 2.0.1
      micromark-util-encode: 2.0.0
      micromark-util-normalize-identifier: 2.0.0
      micromark-util-resolve-all: 2.0.0
      micromark-util-sanitize-uri: 2.0.0
      micromark-util-subtokenize: 2.0.1
      micromark-util-symbol: 2.0.0
      micromark-util-types: 2.0.0
    transitivePeerDependencies:
      - supports-color

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.3:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  motion-dom@12.11.0:
    dependencies:
      motion-utils: 12.9.4

  motion-utils@12.9.4: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nano-css@5.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0
      css-tree: 1.1.3
      csstype: 3.1.3
      fastest-stable-stringify: 2.0.2
      inline-style-prefixer: 7.0.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rtl-css-js: 1.16.1
      stacktrace-js: 2.0.2
      stylis: 4.3.4

  nanoid@3.3.7: {}

  natural-compare@1.4.0: {}

  next-tick@1.1.0: {}

  next@14.2.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@next/env': 14.2.3
      '@swc/helpers': 0.5.5
      busboy: 1.6.0
      caniuse-lite: 1.0.30001655
      graceful-fs: 4.2.11
      postcss: 8.4.31
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      styled-jsx: 5.1.1(react@18.3.1)
    optionalDependencies:
      '@next/swc-darwin-arm64': 14.2.3
      '@next/swc-darwin-x64': 14.2.3
      '@next/swc-linux-arm64-gnu': 14.2.3
      '@next/swc-linux-arm64-musl': 14.2.3
      '@next/swc-linux-x64-gnu': 14.2.3
      '@next/swc-linux-x64-musl': 14.2.3
      '@next/swc-win32-arm64-msvc': 14.2.3
      '@next/swc-win32-ia32-msvc': 14.2.3
      '@next/swc-win32-x64-msvc': 14.2.3
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros

  node-gyp-build@4.8.2: {}

  normalize-path@3.0.0: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.2: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3

  object.values@1.2.0:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  or@0.2.0: {}

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  package-json-from-dist@1.0.0: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-entities@4.0.1:
    dependencies:
      '@types/unist': 2.0.11
      character-entities: 2.0.2
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.0.2
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-type@4.0.0: {}

  picocolors@1.0.1: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pirates@4.0.6: {}

  possible-typed-array-names@1.0.0: {}

  postcss-import@15.1.0(postcss@8.4.41):
    dependencies:
      postcss: 8.4.41
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8

  postcss-js@4.0.1(postcss@8.4.41):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.41

  postcss-load-config@4.0.2(postcss@8.4.41):
    dependencies:
      lilconfig: 3.1.2
      yaml: 2.5.0
    optionalDependencies:
      postcss: 8.4.41

  postcss-nested@6.2.0(postcss@8.4.41):
    dependencies:
      postcss: 8.4.41
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0

  postcss@8.4.41:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0

  prelude-ls@1.2.1: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  property-information@6.5.0: {}

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-is@16.13.1: {}

  react-markdown@9.0.1(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      '@types/hast': 3.0.4
      '@types/react': 18.3.5
      devlop: 1.1.0
      hast-util-to-jsx-runtime: 2.3.2
      html-url-attributes: 3.0.1
      mdast-util-to-hast: 13.2.0
      react: 18.3.1
      remark-parse: 11.0.0
      remark-rehype: 11.1.1
      unified: 11.0.5
      unist-util-visit: 5.0.0
      vfile: 6.0.3
    transitivePeerDependencies:
      - supports-color

  react-remove-scroll-bar@2.3.6(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-style-singleton: 2.2.1(@types/react@18.3.5)(react@18.3.1)
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  react-remove-scroll@2.5.10(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.6(@types/react@18.3.5)(react@18.3.1)
      react-style-singleton: 2.2.1(@types/react@18.3.5)(react@18.3.1)
      tslib: 2.7.0
      use-callback-ref: 1.3.2(@types/react@18.3.5)(react@18.3.1)
      use-sidecar: 1.1.2(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5

  react-style-singleton@2.2.1(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      get-nonce: 1.0.1
      invariant: 2.2.4
      react: 18.3.1
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  react-textarea-autosize@8.5.3(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
      use-composed-ref: 1.3.0(react@18.3.1)
      use-latest: 1.2.1(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  react-universal-interface@0.6.2(react@18.3.1)(tslib@2.7.0):
    dependencies:
      react: 18.3.1
      tslib: 2.7.0

  react-use-websocket@4.8.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-use@17.5.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@types/js-cookie': 2.2.7
      '@xobotyi/scrollbar-width': 1.9.5
      copy-to-clipboard: 3.3.3
      fast-deep-equal: 3.1.3
      fast-shallow-equal: 1.0.0
      js-cookie: 2.2.1
      nano-css: 5.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-universal-interface: 0.6.2(react@18.3.1)(tslib@2.7.0)
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      set-harmonic-interval: 1.0.1
      throttle-debounce: 3.0.1
      ts-easing: 0.2.0
      tslib: 2.7.0

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  reflect.getprototypeof@1.0.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      which-builtin-type: 1.1.4

  regenerator-runtime@0.14.1: {}

  regexp.prototype.flags@1.5.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  remark-parse@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.1
      micromark-util-types: 2.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-rehype@11.1.1:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      mdast-util-to-hast: 13.2.0
      unified: 11.0.5
      vfile: 6.0.3

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  reusify@1.0.4: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rtl-css-js@1.16.1:
    dependencies:
      '@babel/runtime': 7.25.6

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.2:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5

  safe-regex-test@1.0.3:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  screenfull@5.2.0: {}

  scroll-into-view-if-needed@3.0.10:
    dependencies:
      compute-scroll-into-view: 3.1.0

  semver@6.3.1: {}

  semver@7.6.3: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-harmonic-interval@1.0.1: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2

  signal-exit@4.1.0: {}

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  slash@3.0.0: {}

  source-map-js@1.2.0: {}

  source-map@0.5.6: {}

  source-map@0.6.1: {}

  space-separated-tokens@2.0.2: {}

  stack-generator@2.0.10:
    dependencies:
      stackframe: 1.3.4

  stackframe@1.3.4: {}

  stacktrace-gps@3.1.2:
    dependencies:
      source-map: 0.5.6
      stackframe: 1.3.4

  stacktrace-js@2.0.2:
    dependencies:
      error-stack-parser: 2.1.4
      stack-generator: 2.0.10
      stacktrace-gps: 3.1.2

  stop-iteration-iterator@1.0.0:
    dependencies:
      internal-slot: 1.0.7

  streamsearch@1.1.0: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.includes@2.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3

  string.prototype.matchall@4.0.11:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      regexp.prototype.flags: 1.5.2
      set-function-name: 2.0.2
      side-channel: 1.0.6

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3

  string.prototype.trim@1.2.9:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trimend@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.0.1

  strip-bom@3.0.0: {}

  strip-json-comments@3.1.1: {}

  style-to-object@1.0.8:
    dependencies:
      inline-style-parser: 0.2.4

  styled-jsx@5.1.1(react@18.3.1):
    dependencies:
      client-only: 0.0.1
      react: 18.3.1

  stylis@4.3.4: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  tabbable@6.2.0: {}

  tailwind-merge@1.14.0: {}

  tailwind-merge@2.6.0: {}

  tailwind-variants@0.1.20(tailwindcss@3.4.10):
    dependencies:
      tailwind-merge: 1.14.0
      tailwindcss: 3.4.10

  tailwindcss@3.4.10:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.2
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.6
      lilconfig: 2.1.0
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.0.1
      postcss: 8.4.41
      postcss-import: 15.1.0(postcss@8.4.41)
      postcss-js: 4.0.1(postcss@8.4.41)
      postcss-load-config: 4.0.2(postcss@8.4.41)
      postcss-nested: 6.2.0(postcss@8.4.41)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.8
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  tapable@2.2.1: {}

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throttle-debounce@3.0.1: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  trim-lines@3.0.1: {}

  trough@2.2.0: {}

  ts-api-utils@1.3.0(typescript@5.5.4):
    dependencies:
      typescript: 5.5.4

  ts-easing@0.2.0: {}

  ts-interface-checker@0.1.13: {}

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@2.7.0: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  type@2.7.3: {}

  typed-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13

  typed-array-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-byte-offset@1.0.2:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-length@1.0.6:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0

  typedarray-to-buffer@3.1.5:
    dependencies:
      is-typedarray: 1.0.0

  typescript@5.5.4: {}

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  undici-types@6.19.8: {}

  unified@11.0.5:
    dependencies:
      '@types/unist': 3.0.3
      bail: 2.0.2
      devlop: 1.1.0
      extend: 3.0.2
      is-plain-obj: 4.1.0
      trough: 2.2.0
      vfile: 6.0.3

  unist-util-is@6.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-position@5.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-stringify-position@4.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-visit-parents@6.0.1:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-callback-ref@1.3.2(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  use-composed-ref@1.3.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  use-isomorphic-layout-effect@1.1.2(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  use-latest@1.2.1(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      use-isomorphic-layout-effect: 1.1.2(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5

  use-sidecar@1.1.2(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 18.3.1
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  use-sync-external-store@1.2.2(react@18.3.1):
    dependencies:
      react: 18.3.1

  utf-8-validate@5.0.10:
    dependencies:
      node-gyp-build: 4.8.2

  util-deprecate@1.0.2: {}

  vfile-message@4.0.2:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0

  vfile@6.0.3:
    dependencies:
      '@types/unist': 3.0.3
      vfile-message: 4.0.2

  websocket@1.0.35:
    dependencies:
      bufferutil: 4.0.8
      debug: 2.6.9
      es5-ext: 0.10.64
      typedarray-to-buffer: 3.1.5
      utf-8-validate: 5.0.10
      yaeti: 0.0.6
    transitivePeerDependencies:
      - supports-color

  whatwg-fetch@3.6.20: {}

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-builtin-type@1.1.4:
    dependencies:
      function.prototype.name: 1.1.6
      has-tostringtag: 1.0.2
      is-async-function: 2.0.0
      is-date-object: 1.0.5
      is-finalizationregistry: 1.0.2
      is-generator-function: 1.0.10
      is-regex: 1.1.4
      is-weakref: 1.0.2
      isarray: 2.0.5
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.3

  which-typed-array@1.1.15:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  yaeti@0.0.6: {}

  yaml@2.5.0: {}

  yocto-queue@0.1.0: {}

  zustand@4.5.5(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      use-sync-external-store: 1.2.2(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      react: 18.3.1

  zwitch@2.0.4: {}
