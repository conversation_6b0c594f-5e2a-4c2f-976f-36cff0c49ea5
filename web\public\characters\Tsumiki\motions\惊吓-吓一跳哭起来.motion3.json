{"Version": 3, "Meta": {"Duration": 15.867, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 468, "TotalPointCount": 1356, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 0, 4.633, 0, 1, 4.722, 0, 4.811, 30, 4.9, 30, 1, 4.978, 30, 5.056, -30, 5.133, -30, 1, 5.211, -30, 5.289, 30, 5.367, 30, 1, 5.456, 30, 5.544, -30, 5.633, -30, 1, 5.722, -30, 5.811, 30, 5.9, 30, 1, 5.978, 30, 6.056, -30, 6.133, -30, 1, 6.211, -30, 6.289, 30, 6.367, 30, 1, 6.456, 30, 6.544, -30, 6.633, -30, 1, 7, -30, 7.367, 30, 7.733, 30, 1, 7.889, 30, 8.044, 30, 8.2, 30, 1, 8.556, 30, 8.911, 30, 9.267, 30, 1, 9.433, 30, 9.6, 30, 9.767, 30, 1, 10.2, 30, 10.633, 30, 11.067, 30, 1, 11.5, 30, 11.933, 30, 12.367, 30, 1, 12.8, 30, 13.233, 0, 13.667, 0, 1, 14.389, 0, 15.111, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 0, 4.633, 0, 1, 4.722, 0, 4.811, 0, 4.9, 0, 1, 4.978, 0, 5.056, 0, 5.133, 0, 1, 5.211, 0, 5.289, 0, 5.367, 0, 1, 5.456, 0, 5.544, 0, 5.633, 0, 1, 5.722, 0, 5.811, 0, 5.9, 0, 1, 5.978, 0, 6.056, 0, 6.133, 0, 1, 6.211, 0, 6.289, 0, 6.367, 0, 1, 6.456, 0, 6.544, 0, 6.633, 0, 1, 7, 0, 7.367, -30, 7.733, -30, 1, 7.889, -30, 8.044, -30, 8.2, -30, 1, 8.378, -30, 8.556, -14, 8.733, -14, 1, 8.911, -14, 9.089, -30, 9.267, -30, 1, 9.433, -30, 9.6, -30, 9.767, -30, 1, 9.989, -30, 10.211, -14, 10.433, -14, 1, 10.644, -14, 10.856, -30, 11.067, -30, 1, 11.5, -30, 11.933, 0, 12.367, 0, 1, 12.8, 0, 13.233, 0, 13.667, 0, 1, 14.389, 0, 15.111, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.356, 0, 0.711, -15, 1.067, -15, 1, 1.922, -15, 2.778, 15, 3.633, 15, 1, 3.767, 15, 3.9, 15, 4.033, 15, 1, 4.233, 15, 4.433, 0, 4.633, 0, 1, 6.778, 0, 8.922, 0, 11.067, 0, 1, 11.178, 0, 11.289, -15, 11.4, -15, 1, 11.622, -15, 11.844, 30, 12.067, 30, 1, 12.8, 30, 13.533, 0, 14.267, 0, 1, 14.444, 0, 14.622, 15, 14.8, 15, 1, 15.144, 15, 15.489, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.356, 1, 0.711, 1, 1.067, 1, 1, 1.311, 1, 1.556, 1, 1.8, 1, 1, 1.856, 1, 1.911, 0, 1.967, 0, 1, 2.022, 0, 2.078, 1, 2.133, 1, 1, 2.178, 1, 2.222, 0, 2.267, 0, 1, 2.322, 0, 2.378, 1, 2.433, 1, 1, 2.967, 1, 3.5, 1, 4.033, 1, 1, 4.1, 1, 4.167, 1.2, 4.233, 1.2, 1, 4.367, 1.2, 4.5, 0, 4.633, 0, 1, 5.444, 0, 6.256, 0, 7.067, 0, 1, 7.367, 0, 7.667, 0.55, 7.967, 0.55, 1, 8.389, 0.55, 8.811, 0.55, 9.233, 0.55, 1, 9.311, 0.55, 9.389, 0, 9.467, 0, 1, 9.544, 0, 9.622, 0.55, 9.7, 0.55, 1, 10.256, 0.55, 10.811, 0.55, 11.367, 0.55, 1, 11.7, 0.55, 12.033, 0.8, 12.367, 0.8, 1, 13, 0.8, 13.633, 0.8, 14.267, 0.8, 1, 14.789, 0.8, 15.311, 1, 15.833, 1, 0, 15.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.367, 0, 1.667, 0, 1.967, 0, 1, 2.067, 0, 2.167, 0, 2.267, 0, 1, 3.056, 0, 3.844, 1, 4.633, 1, 1, 5.444, 1, 6.256, 1, 7.067, 1, 1, 7.867, 1, 8.667, 0, 9.467, 0, 1, 10.1, 0, 10.733, 0, 11.367, 0, 1, 12.333, 0, 13.3, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.356, 1, 0.711, 1, 1.067, 1, 1, 1.311, 1, 1.556, 1, 1.8, 1, 1, 1.856, 1, 1.911, 0, 1.967, 0, 1, 2.022, 0, 2.078, 1, 2.133, 1, 1, 2.178, 1, 2.222, 0, 2.267, 0, 1, 2.322, 0, 2.378, 1, 2.433, 1, 1, 2.967, 1, 3.5, 1, 4.033, 1, 1, 4.1, 1, 4.167, 1.2, 4.233, 1.2, 1, 4.367, 1.2, 4.5, 0, 4.633, 0, 1, 5.444, 0, 6.256, 0, 7.067, 0, 1, 7.367, 0, 7.667, 0.55, 7.967, 0.55, 1, 8.389, 0.55, 8.811, 0.55, 9.233, 0.55, 1, 9.311, 0.55, 9.389, 0, 9.467, 0, 1, 9.544, 0, 9.622, 0.55, 9.7, 0.55, 1, 10.256, 0.55, 10.811, 0.55, 11.367, 0.55, 1, 11.7, 0.55, 12.033, 0.8, 12.367, 0.8, 1, 13, 0.8, 13.633, 0.8, 14.267, 0.8, 1, 14.789, 0.8, 15.311, 1, 15.833, 1, 0, 15.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.733, 0, 2.4, 0, 3.067, 0, 1, 3.589, 0, 4.111, 1, 4.633, 1, 1, 5.444, 1, 6.256, 1, 7.067, 1, 1, 7.867, 1, 8.667, 0, 9.467, 0, 1, 10.1, 0, 10.733, 0, 11.367, 0, 1, 12.333, 0, 13.3, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 1, 4.633, 1, 1, 5.444, 1, 6.256, 1, 7.067, 1, 1, 8.5, 1, 9.933, 1, 11.367, 1, 1, 11.7, 1, 12.033, 0, 12.367, 0, 1, 13.278, 0, 14.189, 0, 15.1, 0, 1, 15.344, 0, 15.589, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 0, 3.633, 0, 1, 3.767, 0, 3.9, 0, 4.033, 0, 1, 4.233, 0, 4.433, 1, 4.633, 1, 1, 5.444, 1, 6.256, 1, 7.067, 1, 1, 7.289, 1, 7.511, 1, 7.733, 1, 1, 8.944, 1, 10.156, 1, 11.367, 1, 1, 11.7, 1, 12.033, 0, 12.367, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 4.2, 0, 7.333, 0, 10.467, 0, 1, 10.667, 0, 10.867, -1, 11.067, -1, 1, 11.167, -1, 11.267, -1, 11.367, -1, 1, 11.7, -1, 12.033, 1, 12.367, 1, 1, 12.733, 1, 13.1, 0, 13.467, 0, 1, 14.256, 0, 15.044, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.356, -0.5, 0.711, -0.5, 1.067, -0.5, 1, 4.2, -0.5, 7.333, -1, 10.467, -1, 1, 10.667, -1, 10.867, -1, 11.067, -1, 1, 11.167, -1, 11.267, -1, 11.367, -1, 1, 11.7, -1, 12.033, -0.5, 12.367, -0.5, 1, 12.733, -0.5, 13.1, -0.5, 13.467, -0.5, 1, 14.256, -0.5, 15.044, -0.5, 15.833, -0.5, 0, 15.867, -0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.356, 1, 0.711, 1, 1.067, 1, 1, 2.056, 1, 3.044, 0.854, 4.033, 0.5, 1, 4.1, 0.476, 4.167, 0, 4.233, 0, 1, 4.367, 0, 4.5, 0, 4.633, 0, 1, 5.444, 0, 6.256, 0, 7.067, 0, 1, 8.5, 0, 9.933, 0, 11.367, 0, 1, 11.7, 0, 12.033, 1, 12.367, 1, 1, 13, 1, 13.633, 1, 14.267, 1, 1, 14.789, 1, 15.311, 1, 15.833, 1, 0, 15.867, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.311, 0, 1.556, 0, 1.8, 0, 1, 1.856, 0, 1.911, -1, 1.967, -1, 1, 2.022, -1, 2.078, 0, 2.133, 0, 1, 2.178, 0, 2.222, -1, 2.267, -1, 1, 2.322, -1, 2.378, 0, 2.433, 0, 1, 2.967, 0, 3.5, 0, 4.033, 0, 1, 4.233, 0, 4.433, -1, 4.633, -1, 1, 5.444, -1, 6.256, -1, 7.067, -1, 1, 7.367, -1, 7.667, -0.51, 7.967, -0.51, 1, 8.389, -0.51, 8.811, -0.51, 9.233, -0.51, 1, 9.311, -0.51, 9.389, -1, 9.467, -1, 1, 9.544, -1, 9.622, -0.51, 9.7, -0.51, 1, 10.256, -0.51, 10.811, -0.51, 11.367, -0.51, 1, 11.7, -0.51, 12.033, 0, 12.367, 0, 1, 13, 0, 13.633, -0.24, 14.267, -0.24, 1, 14.789, -0.24, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.311, 0, 1.556, 0, 1.8, 0, 1, 1.856, 0, 1.911, -1, 1.967, -1, 1, 2.022, -1, 2.078, 0, 2.133, 0, 1, 2.178, 0, 2.222, -1, 2.267, -1, 1, 2.322, -1, 2.378, 0, 2.433, 0, 1, 2.967, 0, 3.5, 0, 4.033, 0, 1, 4.233, 0, 4.433, -1, 4.633, -1, 1, 5.444, -1, 6.256, -1, 7.067, -1, 1, 7.367, -1, 7.667, -0.51, 7.967, -0.51, 1, 8.389, -0.51, 8.811, -0.51, 9.233, -0.51, 1, 9.311, -0.51, 9.389, -1, 9.467, -1, 1, 9.544, -1, 9.622, -0.51, 9.7, -0.51, 1, 10.256, -0.51, 10.811, -0.51, 11.367, -0.51, 1, 11.7, -0.51, 12.033, 0, 12.367, 0, 1, 13, 0, 13.633, -0.24, 14.267, -0.24, 1, 14.789, -0.24, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, -1, 4.633, -1, 1, 5.444, -1, 6.256, -1, 7.067, -1, 1, 8.5, -1, 9.933, -1, 11.367, -1, 1, 11.7, -1, 12.033, 0, 12.367, 0, 1, 13, 0, 13.633, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, -1, 4.633, -1, 1, 5.444, -1, 6.256, -1, 7.067, -1, 1, 8.5, -1, 9.933, -1, 11.367, -1, 1, 11.7, -1, 12.033, 0, 12.367, 0, 1, 13, 0, 13.633, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 1, 4.633, 1, 1, 5.444, 1, 6.256, 1, 7.067, 1, 1, 8.5, 1, 9.933, 1, 11.367, 1, 1, 11.7, 1, 12.033, 1, 12.367, 1, 1, 13, 1, 13.633, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 1, 4.633, 1, 1, 5.444, 1, 6.256, 1, 7.067, 1, 1, 8.5, 1, 9.933, 1, 11.367, 1, 1, 11.7, 1, 12.033, 1, 12.367, 1, 1, 13, 1, 13.633, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, -1, 4.633, -1, 1, 5.444, -1, 6.256, -1, 7.067, -1, 1, 8.5, -1, 9.933, -1, 11.367, -1, 1, 11.7, -1, 12.033, -0.827, 12.367, -0.6, 1, 13, -0.169, 13.633, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, -1, 4.633, -1, 1, 5.444, -1, 6.256, -1, 7.067, -1, 1, 8.5, -1, 9.933, -1, 11.367, -1, 1, 11.7, -1, 12.033, -0.827, 12.367, -0.6, 1, 13, -0.169, 13.633, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.356, 1, 0.711, 1, 1.067, 1, 1, 2.056, 1, 3.044, 0, 4.033, 0, 1, 4.233, 0, 4.433, 1, 4.633, 1, 1, 4.722, 1, 4.811, 0.45, 4.9, 0.45, 1, 4.978, 0.45, 5.056, 1, 5.133, 1, 1, 5.211, 1, 5.289, 0.45, 5.367, 0.45, 1, 5.444, 0.45, 5.522, 1, 5.6, 1, 1, 5.678, 1, 5.756, 0.45, 5.833, 0.45, 1, 5.911, 0.45, 5.989, 1, 6.067, 1, 1, 6.144, 1, 6.222, 0.45, 6.3, 0.45, 1, 6.378, 0.45, 6.456, 1, 6.533, 1, 1, 6.711, 1, 6.889, 0.618, 7.067, 0.45, 1, 7.289, 0.24, 7.511, 0.219, 7.733, 0.01, 1, 7.889, -0.136, 8.044, -0.55, 8.2, -0.55, 1, 8.378, -0.55, 8.556, 0.01, 8.733, 0.01, 1, 8.911, 0.01, 9.089, -0.55, 9.267, -0.55, 1, 9.433, -0.55, 9.6, 0.01, 9.767, 0.01, 1, 9.989, 0.01, 10.211, -0.55, 10.433, -0.55, 1, 10.667, -0.55, 10.9, 0.01, 11.133, 0.01, 1, 11.356, 0.01, 11.578, 0.021, 11.8, -0.18, 1, 11.989, -0.351, 12.178, -1, 12.367, -1, 1, 12.567, -1, 12.767, -0.17, 12.967, -0.17, 1, 13.244, -0.17, 13.522, -1, 13.8, -1, 1, 13.956, -1, 14.111, -0.349, 14.267, -0.03, 1, 14.356, 0.152, 14.444, 0.134, 14.533, 0.26, 1, 14.622, 0.386, 14.711, 0.53, 14.8, 0.53, 1, 14.9, 0.53, 15, 0, 15.1, 0, 1, 15.344, 0, 15.589, 1, 15.833, 1, 0, 15.867, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.056, 0, 3.044, 1, 4.033, 1, 1, 4.233, 1, 4.433, 1, 4.633, 1, 1, 4.722, 1, 4.811, 0.81, 4.9, 0.81, 1, 4.978, 0.81, 5.056, 1, 5.133, 1, 1, 5.211, 1, 5.289, 0.81, 5.367, 0.81, 1, 5.444, 0.81, 5.522, 1, 5.6, 1, 1, 5.678, 1, 5.756, 0.81, 5.833, 0.81, 1, 5.911, 0.81, 5.989, 1, 6.067, 1, 1, 6.144, 1, 6.222, 0.81, 6.3, 0.81, 1, 6.378, 0.81, 6.456, 1, 6.533, 1, 1, 6.711, 1, 6.889, 0.81, 7.067, 0.81, 1, 7.289, 0.81, 7.511, 1, 7.733, 1, 1, 7.889, 1, 8.044, 0.83, 8.2, 0.83, 1, 8.378, 0.83, 8.556, 1, 8.733, 1, 1, 8.911, 1, 9.089, 0.83, 9.267, 0.83, 1, 9.433, 0.83, 9.6, 1, 9.767, 1, 1, 9.989, 1, 10.211, 0.83, 10.433, 0.83, 1, 10.667, 0.83, 10.9, 1, 11.133, 1, 1, 11.356, 1, 11.578, 0.92, 11.8, 0.58, 1, 11.989, 0.291, 12.178, 0, 12.367, 0, 1, 12.567, 0, 12.767, 0, 12.967, 0, 1, 13.244, 0, 13.522, 0, 13.8, 0, 1, 13.956, 0, 14.111, 0.609, 14.267, 0.88, 1, 14.356, 1.035, 14.444, 1, 14.533, 1, 1, 14.622, 1, 14.711, 0.999, 14.8, 0.75, 1, 14.9, 0.47, 15, 0, 15.1, 0, 1, 15.344, 0, 15.589, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamCheek_01", "Segments": [0, 0, 0, 4.033, 0, 1, 4.233, 0, 4.433, 1, 4.633, 1, 1, 7.756, 1, 10.878, 1, 14, 1, 1, 14.611, 1, 15.222, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamCheek_02", "Segments": [0, 0, 0, 4.633, 0, 1, 7.756, 0, 10.878, 0, 14, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamCheek_03", "Segments": [0, 0, 0, 4.633, 0, 1, 7.756, 0, 10.878, 0, 14, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamCheek_04", "Segments": [0, 0, 0, 4.033, 0, 1, 4.878, 0, 5.722, 1, 6.567, 1, 1, 7.322, 1, 8.078, 1, 8.833, 1, 1, 11.167, 1, 13.5, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 3.067, 0, 5.067, 0, 7.067, 0, 1, 7.289, 0, 7.511, 10, 7.733, 10, 1, 8.844, 10, 9.956, 10, 11.067, 10, 1, 11.5, 10, 11.933, 0, 12.367, 0, 1, 13.522, 0, 14.678, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.922, 0, 2.778, -10, 3.633, -10, 1, 3.767, -10, 3.9, -10, 4.033, -10, 1, 4.1, -10, 4.167, 5, 4.233, 5, 1, 4.3, 5, 4.367, -10, 4.433, -10, 1, 4.5, -10, 4.567, -10, 4.633, -10, 1, 4.689, -10, 4.744, 0, 4.8, 0, 1, 4.878, 0, 4.956, -10, 5.033, -10, 1, 5.111, -10, 5.189, 0, 5.267, 0, 1, 5.344, 0, 5.422, -10, 5.5, -10, 1, 5.589, -10, 5.678, 0, 5.767, 0, 1, 5.844, 0, 5.922, -10, 6, -10, 1, 6.078, -10, 6.156, 0, 6.233, 0, 1, 6.311, 0, 6.389, -10, 6.467, -10, 1, 6.556, -10, 6.644, 0, 6.733, 0, 1, 6.844, 0, 6.956, 0, 7.067, 0, 1, 7.289, 0, 7.511, -10, 7.733, -10, 1, 7.889, -10, 8.044, -5, 8.2, -5, 1, 8.378, -5, 8.556, -10, 8.733, -10, 1, 8.911, -10, 9.089, 0, 9.267, 0, 1, 9.433, 0, 9.6, -10, 9.767, -10, 1, 9.989, -10, 10.211, -5, 10.433, -5, 1, 10.644, -5, 10.856, -10, 11.067, -10, 1, 11.5, -10, 11.933, 0, 12.367, 0, 1, 13.522, 0, 14.678, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.356, 0, 0.711, -2, 1.067, -2, 1, 3.067, -2, 5.067, 0, 7.067, 0, 1, 7.289, 0, 7.511, 0, 7.733, 0, 1, 8.844, 0, 9.956, 0, 11.067, 0, 1, 11.244, 0, 11.422, 2, 11.6, 2, 1, 11.856, 2, 12.111, 0, 12.367, 0, 1, 13.522, 0, 14.678, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 4.833, 0, 8.6, 0, 12.367, 0, 1, 13.522, 0, 14.678, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, -0.41, 4.633, -0.41, 1, 5.444, -0.41, 6.256, 0, 7.067, 0, 1, 8.4, 0, 9.733, 0, 11.067, 0, 1, 11.5, 0, 11.933, 0, 12.367, 0, 1, 13.522, 0, 14.678, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.433, 0, 0.867, -1, 1.3, -1, 1, 1.433, -1, 1.567, -0.879, 1.7, -0.65, 1, 2.344, 0.455, 2.989, 1, 3.633, 1, 1, 3.833, 1, 4.033, -0.67, 4.233, -0.67, 1, 4.367, -0.67, 4.5, 0, 4.633, 0, 1, 4.722, 0, 4.811, -0.63, 4.9, -0.63, 1, 4.978, -0.63, 5.056, 0.23, 5.133, 0.23, 1, 5.211, 0.23, 5.289, -0.48, 5.367, -0.48, 1, 5.456, -0.48, 5.544, 0.2, 5.633, 0.2, 1, 5.722, 0.2, 5.811, -0.51, 5.9, -0.51, 1, 5.978, -0.51, 6.056, 0.18, 6.133, 0.18, 1, 6.211, 0.18, 6.289, -0.7, 6.367, -0.7, 1, 6.456, -0.7, 6.544, 0.059, 6.633, 0.21, 1, 7, 0.832, 7.367, 1, 7.733, 1, 1, 7.889, 1, 8.044, 0, 8.2, 0, 1, 8.378, 0, 8.556, 1, 8.733, 1, 1, 8.911, 1, 9.089, 0.5, 9.267, 0.5, 1, 9.433, 0.5, 9.6, 1, 9.767, 1, 1, 9.989, 1, 10.211, 0.51, 10.433, 0.51, 1, 10.667, 0.51, 10.9, 1, 11.133, 1, 1, 11.544, 1, 11.956, 0.502, 12.367, 0, 1, 13, -0.773, 13.633, -1, 14.267, -1, 1, 14.789, -1, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.122, 0, 3.178, -1, 4.233, -1, 1, 4.3, -1, 4.367, 1, 4.433, 1, 1, 4.5, 1, 4.567, 0, 4.633, 0, 1, 4.722, 0, 4.811, 1, 4.9, 1, 1, 4.978, 1, 5.056, 0.5, 5.133, 0.5, 1, 5.211, 0.5, 5.289, 1, 5.367, 1, 1, 5.456, 1, 5.544, 0.5, 5.633, 0.5, 1, 5.722, 0.5, 5.811, 1, 5.9, 1, 1, 5.978, 1, 6.056, 0.5, 6.133, 0.5, 1, 6.211, 0.5, 6.289, 1, 6.367, 1, 1, 6.456, 1, 6.544, 0.5, 6.633, 0.5, 1, 6.711, 0.5, 6.789, 0.67, 6.867, 0.67, 1, 6.933, 0.67, 7, 0.644, 7.067, 0.5, 1, 7.2, 0.212, 7.333, 0, 7.467, 0, 1, 7.556, 0, 7.644, 0.05, 7.733, 0.05, 1, 7.889, 0.05, 8.044, 0, 8.2, 0, 1, 10.222, 0, 12.244, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 0, 4.633, 0, 1, 4.689, 0, 4.744, 1, 4.8, 1, 1, 4.878, 1, 4.956, 0, 5.033, 0, 1, 5.111, 0, 5.189, 1, 5.267, 1, 1, 5.344, 1, 5.422, 0, 5.5, 0, 1, 5.589, 0, 5.678, 1, 5.767, 1, 1, 5.844, 1, 5.922, 0, 6, 0, 1, 6.078, 0, 6.156, 1, 6.233, 1, 1, 6.311, 1, 6.389, 0, 6.467, 0, 1, 6.556, 0, 6.644, 1, 6.733, 1, 1, 6.844, 1, 6.956, 0, 7.067, 0, 1, 7.289, 0, 7.511, 0.46, 7.733, 0.46, 1, 7.967, 0.46, 8.2, 0, 8.433, 0, 1, 9.4, 0, 10.367, 0, 11.333, 0, 1, 11.644, 0, 11.956, 0.46, 12.267, 0.46, 1, 12.5, 0.46, 12.733, 0, 12.967, 0, 1, 13.922, 0, 14.878, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 0, 4.633, 0, 1, 5.444, 0, 6.256, 0, 7.067, 0, 1, 7.289, 0, 7.511, 0, 7.733, 0, 1, 7.967, 0, 8.2, 1, 8.433, 1, 1, 9.4, 1, 10.367, 1, 11.333, 1, 1, 11.878, 1, 12.422, 0, 12.967, 0, 1, 13.922, 0, 14.878, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 2.256, 0, 3.444, 0, 4.633, 0, 1, 5.444, 0, 6.256, 0, 7.067, 0, 1, 7.289, 0, 7.511, 0, 7.733, 0, 1, 7.967, 0, 8.2, 1, 8.433, 1, 1, 9.4, 1, 10.367, 1, 11.333, 1, 1, 11.878, 1, 12.422, 0, 12.967, 0, 1, 13.922, 0, 14.878, 0, 15.833, 0, 0, 15.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.356, 0.5, 0.711, 0.5, 1.067, 0.5, 1, 3.522, 0.5, 5.978, 0.5, 8.433, 0.5, 1, 8.578, 0.5, 8.722, 1, 8.867, 1, 1, 9.044, 1, 9.222, 0, 9.4, 0, 1, 9.556, 0, 9.711, 1, 9.867, 1, 1, 10.056, 1, 10.244, 0, 10.433, 0, 1, 10.611, 0, 10.789, 1, 10.967, 1, 1, 11.1, 1, 11.233, 0, 11.367, 0, 1, 12.333, 0, 13.3, 0.5, 14.267, 0.5, 1, 14.789, 0.5, 15.311, 0.5, 15.833, 0.5, 0, 15.867, 0.5]}, {"Target": "Parameter", "Id": "ParamLegL", "Segments": [0, 0, 1, 1.478, 0, 2.956, 0, 4.433, 0, 1, 4.5, 0, 4.567, 1, 4.633, 1, 1, 4.722, 1, 4.811, 0.4, 4.9, 0.4, 1, 4.978, 0.4, 5.056, 1, 5.133, 1, 1, 5.211, 1, 5.289, 0.4, 5.367, 0.4, 1, 5.456, 0.4, 5.544, 1, 5.633, 1, 1, 5.722, 1, 5.811, 0.4, 5.9, 0.4, 1, 5.978, 0.4, 6.056, 1, 6.133, 1, 1, 6.211, 1, 6.289, 0.4, 6.367, 0.4, 1, 6.456, 0.4, 6.544, 1, 6.633, 1, 1, 6.778, 1, 6.922, 0.503, 7.067, 0, 1, 7.289, -0.773, 7.511, -1, 7.733, -1, 1, 9.178, -1, 10.622, -1, 12.067, -1, 1, 12.8, -1, 13.533, 0, 14.267, 0, 1, 14.789, 0, 15.311, 0, 15.833, 0, 0, 15.867, 0]}]}