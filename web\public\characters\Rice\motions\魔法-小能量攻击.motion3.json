{"Version": 3, "Meta": {"Duration": 6, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 54, "TotalSegmentCount": 322, "TotalPointCount": 880, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.144, 0, 0.289, -10, 0.433, -10, 1, 0.578, -10, 0.722, 11.077, 0.867, 15, 1, 1.311, 27.071, 1.756, 30, 2.2, 30, 1, 2.267, 30, 2.333, 30, 2.4, 30, 1, 2.456, 30, 2.511, -30, 2.567, -30, 1, 2.7, -30, 2.833, -27, 2.967, -27, 1, 3.311, -27, 3.656, -27, 4, -27, 1, 4.3, -27, 4.6, 0, 4.9, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.144, 0, 0.289, 27, 0.433, 27, 1, 0.567, 27, 0.7, 8.467, 0.833, 6, 1, 1.289, -2.429, 1.744, -7.126, 2.2, -15, 1, 2.278, -16.344, 2.356, -30, 2.433, -30, 1, 2.478, -30, 2.522, 11.148, 2.567, 21, 1, 2.611, 30.852, 2.656, 30, 2.7, 30, 1, 2.789, 30, 2.878, 0, 2.967, 0, 1, 3.1, 0, 3.233, 6, 3.367, 6, 1, 3.578, 6, 3.789, 6, 4, 6, 1, 4.156, 6, 4.311, 6, 4.467, 6, 1, 4.644, 6, 4.822, 0, 5, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 1, 1, 0.156, 1, 0.311, 1, 0.467, 1, 1, 0.544, 1, 0.622, 0, 0.7, 0, 1, 1.267, 0, 1.833, 0, 2.4, 0, 1, 2.467, 0, 2.533, 1, 2.6, 1, 1, 3.322, 1, 4.044, 1, 4.767, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 1, 1, 0.156, 1, 0.311, 1, 0.467, 1, 1, 0.544, 1, 0.622, 0, 0.7, 0, 1, 1.267, 0, 1.833, 0, 2.4, 0, 1, 2.467, 0, 2.533, 1, 2.6, 1, 1, 3.322, 1, 4.044, 1, 4.767, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 1.589, 0, 3.178, 0, 4.767, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 1.589, 0, 3.178, 0, 4.767, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, -10, 0.333, -10, 1, 0.456, -10, 0.578, 6.298, 0.7, 7, 1, 1.133, 9.49, 1.567, 10, 2, 10, 1, 2.122, 10, 2.244, 10, 2.367, 10, 1, 2.478, 10, 2.589, -10, 2.7, -10, 1, 2.789, -10, 2.878, -7, 2.967, -7, 1, 3.278, -7, 3.589, -7, 3.9, -7, 1, 3.978, -7, 4.056, -7, 4.133, -7, 1, 4.344, -7, 4.556, 0, 4.767, 0, 1, 4.956, 0, 5.144, 0, 5.333, 0, 1, 5.556, 0, 5.778, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 3, 0.333, 3, 1, 0.511, 3, 0.689, 0, 0.867, 0, 1, 1.3, 0, 1.733, 0, 2.167, 0, 1, 2.233, 0, 2.3, -3, 2.367, -3, 1, 2.467, -3, 2.567, 0, 2.667, 0, 1, 3.089, 0, 3.511, 0, 3.933, 0, 1, 4.211, 0, 4.489, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.856, 0, 1.378, 3, 1.9, 3, 1, 2, 3, 2.1, 2.298, 2.2, 0, 1, 2.267, -1.532, 2.333, -3, 2.4, -3, 1, 2.467, -3, 2.533, 3, 2.6, 3, 1, 2.678, 3, 2.756, 0, 2.833, 0, 1, 2.911, 0, 2.989, 1, 3.067, 1, 1, 3.333, 1, 3.6, 0, 3.867, 0, 1, 3.911, 0, 3.956, 0, 4, 0, 1, 4.122, 0, 4.244, 2, 4.367, 2, 1, 4.544, 2, 4.722, 0, 4.9, 0, 1, 5.267, 0, 5.633, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 1, 0.111, 0, 0.222, 5, 0.333, 5, 1, 0.489, 5, 0.644, 4.6, 0.8, 4.6, 1, 1.267, 4.6, 1.733, 4.876, 2.2, 5.4, 1, 2.267, 5.475, 2.333, 5.461, 2.4, 5.6, 1, 2.478, 5.762, 2.556, 10, 2.633, 10, 1, 2.744, 10, 2.856, 7.2, 2.967, 7.2, 1, 3.311, 7.2, 3.656, 7.2, 4, 7.2, 1, 4.256, 7.2, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 1, 0.111, 0, 0.222, 5, 0.333, 5, 1, 0.422, 5, 0.511, 0, 0.6, 0, 1, 1.133, 0, 1.667, 0, 2.2, 0, 1, 2.456, 0, 2.711, 0, 2.967, 0, 1, 3.311, 0, 3.656, 0, 4, 0, 1, 4.256, 0, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegKnee", "Segments": [0, 0, 1, 0.144, 0, 0.289, 3, 0.433, 3, 1, 0.522, 3, 0.611, 0.075, 0.7, 0, 1, 1.167, -0.394, 1.633, -0.614, 2.1, -1, 1, 2.178, -1.064, 2.256, -3, 2.333, -3, 1, 2.433, -3, 2.533, 2, 2.633, 2, 1, 2.767, 2, 2.9, 0, 3.033, 0, 1, 3.356, 0, 3.678, 0, 4, 0, 1, 4.256, 0, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegR", "Segments": [0, 0, 1, 0.722, 0, 1.444, 0, 2.167, 0, 1, 2.344, 0, 2.522, -10, 2.7, -10, 1, 2.889, -10, 3.078, -8, 3.267, -8, 1, 3.511, -8, 3.756, -8, 4, -8, 1, 4.256, -8, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegRUpDw", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.133, 0, 2.233, 3, 2.333, 3, 1, 2.433, 3, 2.533, 0, 2.633, 0, 1, 3.1, 0, 3.567, 0, 4.033, 0, 1, 4.167, 0, 4.3, 1, 4.433, 1, 1, 4.589, 1, 4.744, 0, 4.9, 0, 1, 5.267, 0, 5.633, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 1, 1.333, 0, 2.667, 0, 4, 0, 1, 4.244, 0, 4.489, 0, 4.733, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmR01", "Segments": [0, 0, 1, 0.133, 0, 0.267, 5, 0.4, 5, 1, 0.5, 5, 0.6, -1.703, 0.7, -2, 1, 1.111, -3.221, 1.522, -3.831, 1.933, -5, 1, 2.011, -5.221, 2.089, -9, 2.167, -9, 1, 2.233, -9, 2.3, -8.692, 2.367, -6, 1, 2.4, -4.654, 2.433, 28.535, 2.467, 29, 1, 2.533, 29.93, 2.6, 30, 2.667, 30, 1, 2.756, 30, 2.844, 29, 2.933, 29, 1, 3.022, 29, 3.111, 30, 3.2, 30, 1, 3.489, 30, 3.778, 30, 4.067, 30, 1, 4.356, 30, 4.644, -0.525, 4.933, -0.525, 1, 5.144, -0.525, 5.356, 0, 5.567, 0, 1, 5.711, 0, 5.856, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmR01Y", "Segments": [0, 0, 1, 1.389, 0, 2.778, 0, 4.167, 0, 1, 4.422, 0, 4.678, 0, 4.933, 0, 1, 5.289, 0, 5.644, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmR02", "Segments": [0, 0, 1, 0.133, 0, 0.267, 1, 0.4, 1, 1, 0.522, 1, 0.644, -33.746, 0.767, -34, 1, 1.178, -34.856, 1.589, -35.216, 2, -36, 1, 2.078, -36.148, 2.156, -40, 2.233, -40, 1, 2.3, -40, 2.367, -35.967, 2.433, -24, 1, 2.467, -18.017, 2.5, -2.828, 2.533, 0, 1, 2.578, 3.77, 2.622, 4, 2.667, 4, 1, 2.756, 4, 2.844, 0, 2.933, 0, 1, 3.344, 0, 3.756, 0, 4.167, 0, 1, 4.422, 0, 4.678, 0, 4.933, 0, 1, 5.289, 0, 5.644, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmR02Y", "Segments": [0, -10, 1, 0.133, -10, 0.267, -10, 0.4, -10, 1, 0.522, -10, 0.644, -20, 0.767, -20, 1, 1.178, -20, 1.589, -20, 2, -20, 1, 2.078, -20, 2.156, -20, 2.233, -20, 1, 2.289, -20, 2.344, -18.458, 2.4, -10, 1, 2.422, -6.617, 2.444, 10, 2.467, 10, 1, 2.5, 10, 2.533, 0, 2.567, 0, 1, 3.044, 0, 3.522, 0, 4, 0, 1, 4.256, 0, 4.511, -10, 4.767, -10, 1, 5.178, -10, 5.589, -10, 6, -10]}, {"Target": "Parameter", "Id": "ParamArmR03", "Segments": [0, 0, 1, 0.156, 0, 0.311, 18, 0.467, 18, 1, 0.6, 18, 0.733, -15.591, 0.867, -16, 1, 1.289, -17.295, 1.711, -17.781, 2.133, -19, 1, 2.233, -19.289, 2.333, -30, 2.433, -30, 1, 2.533, -30, 2.633, 11, 2.733, 11, 1, 2.844, 11, 2.956, 0, 3.067, 0, 1, 3.378, 0, 3.689, 0, 4, 0, 1, 4.256, 0, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmL01", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 1.289, 0, 1.778, 0, 2.267, 0, 1, 2.411, 0, 2.556, 7, 2.7, 7, 1, 2.811, 7, 2.922, 3, 3.033, 3, 1, 3.356, 3, 3.678, 3, 4, 3, 1, 4.256, 3, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmL02", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.633, 0, 0.767, 0, 0.9, 0, 1, 1.244, 0, 1.589, 5, 1.933, 5, 1, 2.089, 5, 2.244, 5, 2.4, 5, 1, 2.5, 5, 2.6, -5, 2.7, -5, 1, 2.844, -5, 2.989, -3, 3.133, -3, 1, 3.422, -3, 3.711, -3, 4, -3, 1, 4.256, -3, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmL03", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.367, 0, 1.733, -0.804, 2.1, -4, 1, 2.233, -5.162, 2.367, -8, 2.5, -8, 1, 2.611, -8, 2.722, 0, 2.833, 0, 1, 3, 0, 3.167, -5, 3.333, -5, 1, 3.556, -5, 3.778, -5, 4, -5, 1, 4.256, -5, 4.511, 0, 4.767, 0, 1, 5.178, 0, 5.589, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmChange", "FadeInTime": 0.0, "FadeOutTime": 0.0, "Segments": [0, 0, 1, 0.8, 0, 1.6, 0, 2.4, 0, 2, 2.433, 1, 1, 3.122, 1, 3.811, 1, 4.5, 1, 2, 4.533, 0, 1, 5.022, 0, 5.511, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBookPage", "Segments": [0, 0, 0, 2.5, 0, 0, 2.667, 30, 2, 2.7, 0, 0, 2.833, 30, 2, 2.867, 0, 0, 3.033, 30, 2, 3.067, 0, 0, 3.233, 30, 2, 3.267, 0, 0, 3.5, 30, 2, 3.533, 0, 0, 3.8, 30, 2, 3.833, 0, 0, 4.233, 30, 2, 4.267, 0, 0, 4.767, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFlameOn", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFlame", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFlameShaking", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFlameX", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFlameY", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCharge01On", "Segments": [0, 0, 1, 0.289, 0, 0.578, 0, 0.867, 0, 1, 1.022, 0, 1.178, 1, 1.333, 1, 1, 1.5, 1, 1.667, 1, 1.833, 1, 1, 1.933, 1, 2.033, 0, 2.133, 0, 1, 2.256, 0, 2.378, 0, 2.5, 0, 1, 3.667, 0, 4.833, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCharge01", "Segments": [0, 0, 0, 0.567, 0, 0, 0.6, 2.4, 0, 1.4, 60, 2, 1.433, 2.4, 0, 2.233, 60, 2, 2.267, 2.4, 0, 3.067, 60, 2, 3.1, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLightAOn", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 1.022, 0, 1.111, 1, 1.2, 1, 1, 1.633, 1, 2.067, 1, 2.5, 1, 1, 2.511, 1, 2.522, 0, 2.533, 0, 1, 3.689, 0, 4.844, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLightASize", "Segments": [0, 0, 1, 0.3, 0, 0.6, 0, 0.9, 0, 1, 1, 0, 1.1, 1, 1.2, 1, 1, 1.244, 1, 1.289, 0.9, 1.333, 0.9, 1, 1.378, 0.9, 1.422, 1, 1.467, 1, 1, 1.511, 1, 1.556, 0.9, 1.6, 0.9, 1, 1.644, 0.9, 1.689, 1, 1.733, 1, 1, 1.778, 1, 1.822, 0.9, 1.867, 0.9, 1, 1.911, 0.9, 1.956, 1, 2, 1, 1, 2.044, 1, 2.089, 0.9, 2.133, 0.9, 1, 2.178, 0.9, 2.222, 1, 2.267, 1, 1, 2.311, 1, 2.356, 0, 2.4, 0, 1, 2.444, 0, 2.489, 0.4, 2.533, 0.4, 1, 2.544, 0.4, 2.556, 0, 2.567, 0, 1, 3.711, 0, 4.856, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersA", "Segments": [0, 0, 1, 0.833, 0, 1.667, 0, 2.5, 0, 1, 2.533, 0, 2.567, 4.032, 2.6, 10, 1, 2.678, 23.925, 2.756, 30, 2.833, 30, 2, 2.867, 0, 1, 3.911, 0, 4.956, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicAOn", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.8, 0, 0.933, 15, 1.067, 15, 1, 1.456, 15, 1.844, 15, 2.233, 15, 1, 2.3, 15, 2.367, 13, 2.433, 13, 1, 2.5, 13, 2.567, 30, 2.633, 30, 2, 2.667, 0, 1, 3.778, 0, 4.889, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicARotation", "Segments": [0, 0, 0, 0.5, 0, 0, 1.433, 60, 2, 1.467, 2.143, 0, 2.4, 60, 2, 2.433, 2, 0, 3.067, 60, 2, 3.1, 3, 0, 3.733, 60, 2, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicALight", "Segments": [0, 0, 1, 0.289, 0, 0.578, 0, 0.867, 0, 1, 0.989, 0, 1.111, 0.5, 1.233, 0.5, 1, 1.367, 0.5, 1.5, 0.4, 1.633, 0.4, 1, 1.8, 0.4, 1.967, 0.5, 2.133, 0.5, 1, 2.244, 0.5, 2.356, 0, 2.467, 0, 1, 2.522, 0, 2.578, 1, 2.633, 1, 2, 2.667, 0, 1, 3.778, 0, 4.889, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEffectAX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEffectAY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLightBOn", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLightBSize", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicBOn", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicBRotation", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicBMove", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicBX", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersBOn", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersBSize", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersBThicknesses", "Segments": [0, 0, 1, 2, 0, 4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEffectBX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEffectBY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 0, 1, 30, 2, 1.033, 1, 0, 1.7, 30, 2, 1.733, 1, 0, 2.267, 30, 2, 2.3, 1.875, 0, 2.8, 30, 2, 2.833, 1.875, 0, 3.333, 30, 2, 3.367, 1.875, 0, 3.867, 30, 2, 3.9, 1.875, 0, 4.467, 30, 2, 4.5, 1.875, 0, 5.233, 30, 2, 5.267, 1.875, 0, 6, 30]}, {"Target": "Parameter", "Id": "ParamSkirtX", "Segments": [0, 0, 1, 0.189, 0, 0.378, -0.7, 0.567, -0.7, 1, 0.856, -0.7, 1.144, 0, 1.433, 0, 1, 1.7, 0, 1.967, -0.6, 2.233, -0.6, 1, 2.311, -0.6, 2.389, -0.637, 2.467, -0.5, 1, 2.567, -0.324, 2.667, 1, 2.767, 1, 1, 2.844, 1, 2.922, 0.4, 3, 0.4, 1, 3.111, 0.4, 3.222, 1, 3.333, 1, 1, 3.444, 1, 3.556, 0.8, 3.667, 0.8, 1, 3.767, 0.8, 3.867, 1, 3.967, 1, 1, 4.156, 1, 4.344, 0.2, 4.533, 0.2, 1, 4.778, 0.2, 5.022, 0.4, 5.267, 0.4, 1, 5.511, 0.4, 5.756, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamSkirtY", "Segments": [0, 0, 1, 0.311, 0, 0.622, -0.5, 0.933, -0.5, 1, 1.178, -0.5, 1.422, 0.3, 1.667, 0.3, 1, 1.9, 0.3, 2.133, -0.6, 2.367, -0.6, 1, 2.478, -0.6, 2.589, 1, 2.7, 1, 1, 2.789, 1, 2.878, -0.3, 2.967, -0.3, 1, 3.067, -0.3, 3.167, 1, 3.267, 1, 1, 3.367, 1, 3.467, -0.4, 3.567, -0.4, 1, 3.733, -0.4, 3.9, 0.6, 4.067, 0.6, 1, 4.311, 0.6, 4.556, -0.5, 4.8, -0.5, 1, 5.011, -0.5, 5.222, 0.1, 5.433, 0.1, 1, 5.622, 0.1, 5.811, 0, 6, 0]}]}