{"Version": 3, "Meta": {"Duration": 3.3, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 167, "TotalPointCount": 434, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -15, 1, 0.1, -15, 0.2, -15, 0.3, -15, 1, 0.533, -15, 0.767, -15, 1, -15, 1, 1.111, -15, 1.222, -15, 1.333, -15, 1, 1.6, -15, 1.867, -21, 2.133, -21, 0, 3.3, -21]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.6, 0, 1.867, 5, 2.133, 5, 0, 3.3, 5]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.433, 0, 1.533, 11, 1.633, 11, 1, 1.889, 11, 2.144, -30, 2.4, -30, 0, 3.3, -30]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1.5, 0, 1, 1.5, 1, 1.078, 1.501, 1.156, 1.465, 1.233, 1.298, 1, 1.267, 1.224, 1.3, 1.129, 1.333, 1, 1, 1.378, 0.835, 1.422, -0.007, 1.467, 0, 0, 1.533, 0, 1, 1.589, -0.008, 1.644, 1.008, 1.7, 1, 0, 3.233, 1, 0, 3.3, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1.5, 0, 1, 1.5, 1, 1.078, 1.501, 1.156, 1.465, 1.233, 1.298, 1, 1.267, 1.224, 1.3, 1.129, 1.333, 1, 1, 1.378, 0.835, 1.422, -0.007, 1.467, 0, 0, 1.533, 0, 1, 1.589, -0.008, 1.644, 1.008, 1.7, 1, 0, 3.233, 1, 0, 3.3, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.7, 1, 0.1, 0.7, 0.2, 0.7, 0.3, 0.7, 1, 0.533, 0.7, 0.767, 0.7, 1, 0.7, 1, 1.111, 0.7, 1.222, 0.7, 1.333, 0.7, 0, 3.3, 0.7]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, -0.7, 1, 0.1, -0.7, 0.2, -0.7, 0.3, -0.7, 1, 0.533, -0.7, 0.767, -0.7, 1, -0.7, 1, 1.111, -0.7, 1.222, -0.7, 1.333, -0.7, 0, 3.3, -0.7]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.61, 1, 0.1, 0.61, 0.2, 0.61, 0.3, 0.61, 1, 0.533, 0.61, 0.767, 0.61, 1, 0.61, 1, 1.111, 0.61, 1.222, 0.61, 1.333, 0.61, 0, 3.3, 0.61]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.66, 1, 0.1, 0.66, 0.2, 0.66, 0.3, 0.66, 1, 0.533, 0.66, 0.767, 0.66, 1, 0.66, 1, 1.111, 0.66, 1.222, 0.66, 1.333, 0.66, 0, 3.3, 0.66]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.6, 0, 1.867, 0.54, 2.133, 0.54, 0, 3.3, 0.54]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.6, 0, 1.867, 0.66, 2.133, 0.66, 0, 3.3, 0.66]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.6, 0, 1.867, -0.79, 2.133, -0.79, 0, 3.3, -0.79]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.6, 0, 1.867, -0.84, 2.133, -0.84, 0, 3.3, -0.84]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.1, -1, 0.2, -1, 0.3, -1, 1, 0.533, -1, 0.767, -1, 1, -1, 1, 1.111, -1, 1.222, -1, 1.333, -1, 1, 1.6, -1, 1.867, 1, 2.133, 1, 0, 3.3, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0.011, 0.267, 0.044, 1, 0.311, 0.06, 0.356, 0.082, 0.4, 0.11, 1, 0.422, 0.13, 0.444, 0.405, 0.467, 0.549, 1, 0.489, 0.69, 0.511, 0.743, 0.533, 0.745, 1, 0.556, 0.743, 0.578, 0.583, 0.6, 0.58, 1, 0.622, 0.582, 0.644, 0.665, 0.667, 0.667, 1, 0.7, 0.607, 0.733, 0.283, 0.767, 0.361, 1, 0.8, 0.462, 0.833, 0.077, 0.867, 0, 0, 1.467, 0, 1, 1.478, -0.015, 1.489, 0.333, 1.5, 0.631, 1, 1.511, 1.1, 1.522, 0.981, 1.533, 1, 2, 1.567, 0.867, 1, 1.578, 0.679, 1.589, 0.461, 1.6, 0.471, 1, 1.622, 0.473, 1.644, 0.61, 1.667, 0.612, 1, 1.7, 0.438, 1.733, -0.18, 1.767, 0.025, 1, 1.8, 0.008, 1.833, 0.008, 1.867, 0.008, 1, 1.878, -0.01, 1.889, 0.411, 1.9, 0.773, 3, 1.933, 1, 2, 1.967, 0.875, 1, 1.978, 0.561, 1.989, 0.196, 2, 0.212, 1, 2.022, 0.224, 2.044, 0.968, 2.067, 0.98, 1, 2.089, 0.97, 2.111, 0.324, 2.133, 0.314, 1, 2.144, 0.3, 2.156, 0.614, 2.167, 0.882, 3, 2.2, 1, 0, 2.267, 1, 2, 2.3, 0.902, 1, 2.311, 0.714, 2.322, 0.509, 2.333, 0.518, 1, 2.344, 0.478, 2.356, 0.879, 2.367, 1, 0, 2.533, 1, 2, 2.567, 0.718, 1, 2.6, -0.503, 2.633, 0.254, 2.667, 0.039, 1, 2.678, 0.023, 2.689, 0.407, 2.7, 0.754, 3, 2.733, 1, 0, 2.867, 1, 2, 2.9, 0.909, 1, 2.933, 0.234, 2.967, -0.022, 3, 0, 0, 3.233, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -5, 1, 0.1, -5, 0.2, -5, 0.3, -5, 1, 0.378, -5, 0.456, -8, 0.533, -8, 1, 0.644, -8, 0.756, -8, 0.867, -8, 1, 1.022, -8, 1.178, -8, 1.333, -8, 1, 1.411, -8, 1.489, -8, 1.567, -8, 1, 1.833, -8, 2.1, -10, 2.367, -10, 0, 3.3, -10]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.389, 0, 0.478, 10, 0.567, 10, 1, 0.667, 10, 0.767, 0, 0.867, 0, 1, 1.022, 0, 1.178, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.533, 0, 0.767, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 0, 3.3, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 0, 3.3, 0]}]}