import React, { useState, useRef, useEffect } from 'react';

export const SimpleDropdown = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div 
      ref={dropdownRef}
      className={className} 
      {...props}
    >
      {React.Children.map(children, child => {
        if (child.type === SimpleDropdownTrigger) {
          return React.cloneElement(child, {
            onClick: () => setIsOpen(!isOpen),
          });
        }
        if (child.type === SimpleDropdownMenu) {
          return React.cloneElement(child, {
            isOpen,
          });
        }
        return child;
      })}
    </div>
  );
};

export const SimpleDropdownTrigger = ({ 
  children, 
  onClick,
  className = '', 
  ...props 
}) => {
  return (
    <div 
      className={`cursor-pointer ${className}`} 
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

export const SimpleDropdownMenu = ({ 
  children, 
  isOpen,
  'aria-label': ariaLabel,
  onAction,
  className = '', 
  ...props 
}) => {
  if (!isOpen) return null;
  
  const menuStyles = {
    position: 'absolute',
    zIndex: 1000,
    minWidth: '10rem',
    padding: '0.5rem 0',
    marginTop: '0.125rem',
    backgroundColor: 'white',
    borderRadius: '0.375rem',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  };
  
  return (
    <div 
      style={menuStyles} 
      className={className}
      role="menu"
      aria-label={ariaLabel}
      {...props}
    >
      {React.Children.map(children, child => {
        if (child.type === SimpleDropdownItem && onAction) {
          return React.cloneElement(child, {
            onClick: () => onAction(child.props.key || child.props.textValue),
          });
        }
        return child;
      })}
    </div>
  );
};

export const SimpleDropdownItem = ({ 
  children, 
  textValue,
  onClick,
  className = '', 
  ...props 
}) => {
  const itemStyles = {
    padding: '0.5rem 1rem',
    cursor: 'pointer',
    ':hover': {
      backgroundColor: '#f7fafc',
    },
  };
  
  return (
    <div 
      style={itemStyles} 
      className={className}
      role="menuitem"
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

export default SimpleDropdown;
