import React from 'react';

const cardStyles = {
  base: {
    padding: '16px',
    borderRadius: '12px',
    backgroundColor: '#ffffff',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    transition: 'box-shadow 0.3s ease',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
  },
  hoverable: {
    cursor: 'pointer',
    '&:hover': {
      boxShadow: '0 10px 15px rgba(0, 0, 0, 0.15)',
    }
  },
  flat: {
    boxShadow: 'none',
    border: '1px solid #e0e0e0',
  },
  shadow: {
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.15)',
  }
};

export const SimpleCard = ({ 
  children, 
  className = '', 
  isHoverable = false,
  variant = 'default',
  onClick,
  ...props 
}) => {
  const style = {
    ...cardStyles.base,
    ...(isHoverable && cardStyles.hoverable),
    ...(variant === 'flat' && cardStyles.flat),
    ...(variant === 'shadow' && cardStyles.shadow),
  };
  
  return (
    <div 
      style={style} 
      className={className} 
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

export const SimpleCardHeader = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const style = {
    padding: '8px 16px',
    borderBottom: '1px solid #e0e0e0',
    marginBottom: '8px',
  };
  
  return (
    <div style={style} className={className} {...props}>
      {children}
    </div>
  );
};

export const SimpleCardBody = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const style = {
    padding: '8px 16px',
    flex: '1 1 auto',
  };
  
  return (
    <div style={style} className={className} {...props}>
      {children}
    </div>
  );
};

export const SimpleCardFooter = ({ 
  children, 
  className = '', 
  ...props 
}) => {
  const style = {
    padding: '8px 16px',
    borderTop: '1px solid #e0e0e0',
    marginTop: '8px',
  };
  
  return (
    <div style={style} className={className} {...props}>
      {children}
    </div>
  );
};

export default SimpleCard;
