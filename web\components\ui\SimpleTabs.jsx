import React, { useState } from 'react';

export const SimpleTabs = ({ 
  children, 
  'aria-label': ariaLabel,
  className = '', 
  ...props 
}) => {
  const [activeTab, setActiveTab] = useState(0);
  
  // Filter out only Tab components from children
  const tabs = React.Children.toArray(children).filter(
    child => React.isValidElement(child) && child.type === SimpleTab
  );
  
  const tabTitles = tabs.map(tab => 
    React.isValidElement(tab) ? tab.props.title : ''
  );
  
  const handleTabClick = (index) => {
    setActiveTab(index);
  };
  
  const containerStyles = {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  };
  
  const tabListStyles = {
    display: 'flex',
    borderBottom: '1px solid #e2e8f0',
    marginBottom: '1rem',
  };
  
  const tabButtonStyles = (index) => ({
    padding: '0.5rem 1rem',
    border: 'none',
    background: 'none',
    cursor: 'pointer',
    borderBottom: index === activeTab ? '2px solid #3182ce' : 'none',
    fontWeight: index === activeTab ? 'bold' : 'normal',
    color: index === activeTab ? '#3182ce' : 'inherit',
    marginBottom: '-1px',
  });
  
  return (
    <div style={containerStyles} className={className} {...props} role="tablist" aria-label={ariaLabel}>
      <div style={tabListStyles}>
        {tabTitles.map((title, index) => (
          <button
            key={index}
            style={tabButtonStyles(index)}
            onClick={() => handleTabClick(index)}
            role="tab"
            aria-selected={index === activeTab}
            aria-controls={`tabpanel-${index}`}
            id={`tab-${index}`}
          >
            {title}
          </button>
        ))}
      </div>
      <div>
        {tabs.map((tab, index) => (
          <div
            key={index}
            role="tabpanel"
            id={`tabpanel-${index}`}
            aria-labelledby={`tab-${index}`}
            hidden={index !== activeTab}
          >
            {index === activeTab ? tab : null}
          </div>
        ))}
      </div>
    </div>
  );
};

export const SimpleTab = ({ 
  children, 
  title,
  key,
  className = '', 
  ...props 
}) => {
  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
};

export default SimpleTabs;
